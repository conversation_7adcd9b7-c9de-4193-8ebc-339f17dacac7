<?php
/**
 * Database installation script for ProductVideoPro module
 */

$sql = array();

// Main product videos table
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_video` (
    `id_product_video` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_product` int(10) unsigned NOT NULL,
    `id_shop` int(10) unsigned DEFAULT NULL,
    `video_type` enum("youtube","vimeo","mp4") NOT NULL DEFAULT "youtube",
    `video_url` varchar(500) NOT NULL,
    `video_id` varchar(100) DEFAULT NULL,
    `thumbnail_url` varchar(500) DEFAULT NULL,
    `width` varchar(20) DEFAULT "100%",
    `height` varchar(20) DEFAULT "auto",
    `position` int(10) unsigned NOT NULL DEFAULT 0,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `autoplay` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `controls` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `loop` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `muted` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `show_in_product_page` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `show_in_product_list` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `show_in_additional_info` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `show_in_footer` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `show_in_extra_content` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_product_video`),
    KEY `id_product` (`id_product`),
    KEY `id_shop` (`id_shop`),
    KEY `position` (`position`),
    KEY `active` (`active`),
    KEY `video_type` (`video_type`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Product videos language table for multilingual titles and descriptions
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_video_lang` (
    `id_product_video` int(10) unsigned NOT NULL,
    `id_lang` int(10) unsigned NOT NULL,
    `title` varchar(255) DEFAULT NULL,
    `description` text DEFAULT NULL,
    PRIMARY KEY (`id_product_video`, `id_lang`),
    KEY `id_product_video` (`id_product_video`),
    KEY `id_lang` (`id_lang`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Video settings table for advanced configuration
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_video_settings` (
    `id_setting` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_shop` int(10) unsigned DEFAULT NULL,
    `setting_name` varchar(100) NOT NULL,
    `setting_value` text DEFAULT NULL,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_setting`),
    UNIQUE KEY `shop_setting` (`id_shop`, `setting_name`),
    KEY `id_shop` (`id_shop`),
    KEY `setting_name` (`setting_name`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Video layout configurations table
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_video_layout` (
    `id_layout` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_shop` int(10) unsigned DEFAULT NULL,
    `layout_name` varchar(100) NOT NULL,
    `layout_type` enum("grid","list","carousel","popup","sidebar") NOT NULL DEFAULT "grid",
    `columns` int(2) unsigned NOT NULL DEFAULT 2,
    `show_title` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `show_description` tinyint(1) unsigned NOT NULL DEFAULT 0,
    `thumbnail_size` enum("small","medium","large") NOT NULL DEFAULT "medium",
    `hover_effect` enum("none","zoom","overlay","play") NOT NULL DEFAULT "overlay",
    `popup_enabled` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `responsive_breakpoints` text DEFAULT NULL,
    `custom_css` text DEFAULT NULL,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_layout`),
    KEY `id_shop` (`id_shop`),
    KEY `layout_type` (`layout_type`),
    KEY `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Video categories association table
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_video_category` (
    `id_product_video` int(10) unsigned NOT NULL,
    `id_category` int(10) unsigned NOT NULL,
    `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
    PRIMARY KEY (`id_product_video`, `id_category`),
    KEY `id_product_video` (`id_product_video`),
    KEY `id_category` (`id_category`),
    KEY `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Execute all SQL statements
foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}

// Insert default settings
$default_settings = [
    ['setting_name' => 'default_layout', 'setting_value' => 'grid'],
    ['setting_name' => 'default_columns', 'setting_value' => '2'],
    ['setting_name' => 'enable_popup', 'setting_value' => '1'],
    ['setting_name' => 'enable_autoplay', 'setting_value' => '0'],
    ['setting_name' => 'enable_controls', 'setting_value' => '1'],
    ['setting_name' => 'thumbnail_quality', 'setting_value' => 'medium'],
    ['setting_name' => 'responsive_enabled', 'setting_value' => '1'],
    ['setting_name' => 'lazy_loading', 'setting_value' => '1'],
    ['setting_name' => 'cache_thumbnails', 'setting_value' => '1'],
    ['setting_name' => 'max_videos_per_product', 'setting_value' => '10']
];

foreach ($default_settings as $setting) {
    Db::getInstance()->insert('st_product_video_settings', [
        'setting_name' => pSQL($setting['setting_name']),
        'setting_value' => pSQL($setting['setting_value']),
        'date_add' => date('Y-m-d H:i:s'),
        'date_upd' => date('Y-m-d H:i:s')
    ]);
}

// Insert default layout configuration
Db::getInstance()->insert('st_product_video_layout', [
    'layout_name' => 'Default Grid Layout',
    'layout_type' => 'grid',
    'columns' => 2,
    'show_title' => 1,
    'show_description' => 0,
    'thumbnail_size' => 'medium',
    'hover_effect' => 'overlay',
    'popup_enabled' => 1,
    'responsive_breakpoints' => json_encode([
        'mobile' => ['max_width' => '768px', 'columns' => 1],
        'tablet' => ['max_width' => '1024px', 'columns' => 2],
        'desktop' => ['min_width' => '1025px', 'columns' => 3]
    ]),
    'active' => 1,
    'date_add' => date('Y-m-d H:i:s'),
    'date_upd' => date('Y-m-d H:i:s')
]);
