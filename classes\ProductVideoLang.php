<?php
/**
 * ProductVideoLang class for managing multilingual video data
 */

class ProductVideoLang extends ObjectModel
{
    public $id_product_video;
    public $id_lang;
    public $title;
    public $description;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_product_video_lang',
        'primary' => 'id_product_video',
        'fields' => [
            'id_product_video' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_lang' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'title' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 255],
            'description' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get video language data
     */
    public static function getVideoLangData($id_product_video, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('*');
        $sql->from('st_product_video_lang');
        $sql->where('id_product_video = ' . (int)$id_product_video);
        $sql->where('id_lang = ' . (int)$id_lang);

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->getRow($sql);
    }

    /**
     * Get all language data for a video
     */
    public static function getAllVideoLangData($id_product_video)
    {
        $sql = new DbQuery();
        $sql->select('pvl.*, l.name as lang_name, l.iso_code');
        $sql->from('st_product_video_lang', 'pvl');
        $sql->leftJoin('lang', 'l', 'pvl.id_lang = l.id_lang');
        $sql->where('pvl.id_product_video = ' . (int)$id_product_video);
        $sql->orderBy('l.name ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Update video language data
     */
    public static function updateVideoLangData($id_product_video, $lang_data)
    {
        foreach ($lang_data as $id_lang => $data) {
            $existing = self::getVideoLangData($id_product_video, $id_lang);
            
            if ($existing) {
                // Update existing record
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'st_product_video_lang` SET 
                        `title` = "' . pSQL($data['title']) . '",
                        `description` = "' . pSQL($data['description']) . '"
                        WHERE `id_product_video` = ' . (int)$id_product_video . ' 
                        AND `id_lang` = ' . (int)$id_lang;
            } else {
                // Insert new record
                $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'st_product_video_lang` 
                        (`id_product_video`, `id_lang`, `title`, `description`) 
                        VALUES (' . (int)$id_product_video . ', ' . (int)$id_lang . ', 
                        "' . pSQL($data['title']) . '", "' . pSQL($data['description']) . '")';
            }
            
            Db::getInstance()->execute($sql);
        }

        return true;
    }

    /**
     * Delete all language data for a video
     */
    public static function deleteVideoLangData($id_product_video)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_product_video_lang` 
                WHERE `id_product_video` = ' . (int)$id_product_video;
        
        return Db::getInstance()->execute($sql);
    }

    /**
     * Copy language data from one video to another
     */
    public static function copyVideoLangData($id_product_video_source, $id_product_video_target)
    {
        $source_data = self::getAllVideoLangData($id_product_video_source);
        
        foreach ($source_data as $lang_data) {
            $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'st_product_video_lang` 
                    (`id_product_video`, `id_lang`, `title`, `description`) 
                    VALUES (' . (int)$id_product_video_target . ', ' . (int)$lang_data['id_lang'] . ', 
                    "' . pSQL($lang_data['title']) . '", "' . pSQL($lang_data['description']) . '")';
            
            Db::getInstance()->execute($sql);
        }

        return true;
    }
}
