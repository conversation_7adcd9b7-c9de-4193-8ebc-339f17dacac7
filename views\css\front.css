/**
 * Product Video Pro - Front Office Styles
 */

/* ==========================================================================
   Base Video Container Styles
   ========================================================================== */

.product-videos-container {
    margin: 20px 0;
    clear: both;
}

.product-videos-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.video-item-wrapper {
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.video-item-wrapper.hidden {
    display: none;
}

.product-video-item {
    position: relative;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.product-video-item:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* ==========================================================================
   Video Content Styles
   ========================================================================== */

.video-thumbnail-container {
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.video-thumbnail {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.05);
}

.video-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-thumbnail-container:hover .video-play-overlay {
    opacity: 1;
}

.video-play-button {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007bff;
    font-size: 24px;
    transition: all 0.3s ease;
}

.video-play-button:hover {
    background: #fff;
    transform: scale(1.1);
}

.video-embed-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    background: #000;
}

.video-embed-container iframe,
.video-embed-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.video-duration-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* ==========================================================================
   Video Information Styles
   ========================================================================== */

.video-info {
    padding: 15px;
}

.video-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    line-height: 1.4;
}

.video-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 10px;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
    color: #888;
    margin-bottom: 10px;
}

.video-type {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 500;
}

.video-share-buttons {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.video-share-button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    font-size: 12px;
    transition: all 0.3s ease;
}

.video-share-button:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
}

/* ==========================================================================
   Layout Styles
   ========================================================================== */

/* List Layout */
.videos-list .video-item-wrapper {
    margin-bottom: 20px;
}

.videos-list .product-video-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.videos-list .video-thumbnail-container {
    flex: 0 0 200px;
    max-width: 200px;
}

.videos-list .video-info {
    flex: 1;
    padding: 0;
}

/* Grid Layout */
.videos-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: -10px;
}

.videos-grid .video-item-wrapper {
    flex: 0 0 calc(50% - 10px);
    margin: 10px 0;
}

.videos-grid.columns-1 .video-item-wrapper {
    flex: 0 0 100%;
}

.videos-grid.columns-3 .video-item-wrapper {
    flex: 0 0 calc(33.333% - 14px);
}

.videos-grid.columns-4 .video-item-wrapper {
    flex: 0 0 calc(25% - 15px);
}

/* Carousel Layout */
.videos-carousel {
    position: relative;
}

.videos-carousel .carousel-inner {
    border-radius: 8px;
    overflow: hidden;
}

.videos-carousel .carousel-item {
    transition: transform 0.6s ease-in-out;
}

.videos-carousel .carousel-control-prev,
.videos-carousel .carousel-control-next {
    width: 40px;
    height: 40px;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.videos-carousel:hover .carousel-control-prev,
.videos-carousel:hover .carousel-control-next {
    opacity: 1;
}

.videos-carousel .carousel-control-prev {
    left: 10px;
}

.videos-carousel .carousel-control-next {
    right: 10px;
}

.videos-carousel .carousel-indicators {
    bottom: 10px;
}

.videos-carousel .carousel-indicators li {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 3px;
}

/* Tabs Layout */
.videos-tabs .nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
}

.videos-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #666;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.videos-tabs .nav-link:hover {
    border-color: #007bff;
    color: #007bff;
}

.videos-tabs .nav-link.active {
    background: none;
    border-color: #007bff;
    color: #007bff;
}

.videos-tabs .tab-content {
    min-height: 300px;
}

/* Accordion Layout */
.videos-accordion .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 10px;
    overflow: hidden;
}

.videos-accordion .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
}

.videos-accordion .btn-link {
    width: 100%;
    text-align: left;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    border: none;
    background: none;
    transition: all 0.3s ease;
}

.videos-accordion .btn-link:hover {
    background: #e9ecef;
    color: #007bff;
    text-decoration: none;
}

.videos-accordion .btn-link:focus {
    box-shadow: none;
}

.videos-accordion .card-body {
    padding: 20px;
}

/* ==========================================================================
   View All Link
   ========================================================================== */

.videos-view-all {
    text-align: center;
    margin-top: 20px;
}

.videos-view-all-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.videos-view-all-link:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* ==========================================================================
   Modal Styles
   ========================================================================== */

.video-modal .modal-dialog {
    max-width: 90vw;
    width: 800px;
}

.video-modal .modal-content {
    border-radius: 8px;
    overflow: hidden;
}

.video-modal .modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
}

.video-modal .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.video-modal .modal-body {
    padding: 0;
    background: #000;
}

.video-modal .modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#modal-video-content {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
}

#modal-video-content iframe,
#modal-video-content video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 1200px) {
    .videos-grid.columns-4 .video-item-wrapper {
        flex: 0 0 calc(33.333% - 14px);
    }
}

@media (max-width: 992px) {
    .videos-grid.columns-3 .video-item-wrapper,
    .videos-grid.columns-4 .video-item-wrapper {
        flex: 0 0 calc(50% - 10px);
    }
    
    .videos-list .product-video-item {
        flex-direction: column;
    }
    
    .videos-list .video-thumbnail-container {
        flex: none;
        max-width: none;
    }
    
    .videos-list .video-info {
        padding: 15px 0 0 0;
    }
}

@media (max-width: 768px) {
    .videos-grid .video-item-wrapper {
        flex: 0 0 100%;
    }
    
    .product-videos-title {
        font-size: 16px;
    }
    
    .video-title {
        font-size: 14px;
    }
    
    .video-description {
        font-size: 13px;
    }
    
    .video-play-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .video-modal .modal-dialog {
        max-width: 95vw;
        margin: 10px auto;
    }
    
    .videos-tabs .nav-link {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .video-share-buttons {
        flex-wrap: wrap;
    }
    
    .video-share-button {
        font-size: 11px;
        padding: 5px 10px;
    }
}

@media (max-width: 480px) {
    .product-videos-container {
        margin: 15px 0;
    }
    
    .video-item-wrapper {
        margin-bottom: 15px;
    }
    
    .video-info {
        padding: 12px;
    }
    
    .videos-view-all-link {
        padding: 8px 16px;
        font-size: 14px;
    }
    
    .videos-accordion .btn-link {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .videos-accordion .card-body {
        padding: 15px;
    }
}

/* ==========================================================================
   Loading and Animation States
   ========================================================================== */

.video-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #f8f9fa;
    border-radius: 8px;
}

.video-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.video-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.video-slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

.video-item-wrapper:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.video-thumbnail-container:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .product-videos-container {
        break-inside: avoid;
    }
    
    .video-embed-container {
        display: none;
    }
    
    .video-thumbnail-container::after {
        content: " (Video: " attr(data-video-url) ")";
        font-size: 12px;
        color: #666;
    }
}
