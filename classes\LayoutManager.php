<?php
/**
 * Layout Manager for Product Video Pro
 * Handles 140+ layout combinations for video display
 */

class LayoutManager
{
    // Display positions
    const POSITION_PRODUCT_IMAGES = 'product_images';
    const POSITION_ADDITIONAL_INFO = 'additional_info';
    const POSITION_FOOTER = 'footer';
    const POSITION_EXTRA_CONTENT = 'extra_content';
    const POSITION_PRODUCT_LIST = 'product_list';

    // Layout styles
    const LAYOUT_LIST = 'list';
    const LAYOUT_GRID = 'grid';
    const LAYOUT_CAROUSEL = 'carousel';
    const LAYOUT_TABS = 'tabs';
    const LAYOUT_ACCORDION = 'accordion';

    // Display modes
    const MODE_DIRECT = 'direct';
    const MODE_THUMBNAIL = 'thumbnail';
    const MODE_POPUP = 'popup';

    // Product listing modes
    const LIST_MODE_HOVER_THUMBNAIL = 'hover_thumbnail';
    const LIST_MODE_HOVER_PREVIEW = 'hover_preview';
    const LIST_MODE_CLICK_POPUP = 'click_popup';
    const LIST_MODE_BADGE_ONLY = 'badge_only';
    const LIST_MODE_THUMBNAIL_CORNER = 'thumbnail_corner';
    const LIST_MODE_ICON_ONLY = 'icon_only';

    /**
     * Get all available layout combinations
     */
    public static function getAllLayoutCombinations()
    {
        $combinations = [];
        $positions = self::getPositions();
        $layouts = self::getLayoutStyles();
        $modes = self::getDisplayModes();
        $list_modes = self::getProductListModes();
        
        $combination_id = 1;
        
        foreach ($positions as $position_key => $position_name) {
            if ($position_key === self::POSITION_PRODUCT_LIST) {
                // Product listing has different layout options
                foreach ($list_modes as $list_mode_key => $list_mode_name) {
                    foreach (['small', 'medium', 'large'] as $size) {
                        foreach (['left', 'right', 'center', 'overlay'] as $alignment) {
                            $combinations[$combination_id] = [
                                'id' => $combination_id,
                                'position' => $position_key,
                                'position_name' => $position_name,
                                'layout' => $list_mode_key,
                                'layout_name' => $list_mode_name,
                                'mode' => 'thumbnail',
                                'mode_name' => 'Thumbnail',
                                'size' => $size,
                                'alignment' => $alignment,
                                'description' => sprintf('%s - %s (%s, %s)', 
                                    $position_name, $list_mode_name, ucfirst($size), ucfirst($alignment))
                            ];
                            $combination_id++;
                        }
                    }
                }
            } else {
                // Regular product page positions
                foreach ($layouts as $layout_key => $layout_name) {
                    foreach ($modes as $mode_key => $mode_name) {
                        foreach (['1', '2', '3', '4'] as $columns) {
                            foreach (['small', 'medium', 'large'] as $size) {
                                $combinations[$combination_id] = [
                                    'id' => $combination_id,
                                    'position' => $position_key,
                                    'position_name' => $position_name,
                                    'layout' => $layout_key,
                                    'layout_name' => $layout_name,
                                    'mode' => $mode_key,
                                    'mode_name' => $mode_name,
                                    'columns' => $columns,
                                    'size' => $size,
                                    'description' => sprintf('%s - %s %s (%s cols, %s)', 
                                        $position_name, $layout_name, $mode_name, $columns, ucfirst($size))
                                ];
                                $combination_id++;
                            }
                        }
                    }
                }
            }
        }
        
        return $combinations;
    }

    /**
     * Get available positions
     */
    public static function getPositions()
    {
        return [
            self::POSITION_PRODUCT_IMAGES => 'Product Images Section',
            self::POSITION_ADDITIONAL_INFO => 'Additional Information',
            self::POSITION_FOOTER => 'Product Footer',
            self::POSITION_EXTRA_CONTENT => 'Extra Content',
            self::POSITION_PRODUCT_LIST => 'Product Listings'
        ];
    }

    /**
     * Get available layout styles
     */
    public static function getLayoutStyles()
    {
        return [
            self::LAYOUT_LIST => 'List Layout',
            self::LAYOUT_GRID => 'Grid Layout',
            self::LAYOUT_CAROUSEL => 'Carousel Layout',
            self::LAYOUT_TABS => 'Tabs Layout',
            self::LAYOUT_ACCORDION => 'Accordion Layout'
        ];
    }

    /**
     * Get available display modes
     */
    public static function getDisplayModes()
    {
        return [
            self::MODE_DIRECT => 'Direct Embed',
            self::MODE_THUMBNAIL => 'Thumbnail Click',
            self::MODE_POPUP => 'Popup Modal'
        ];
    }

    /**
     * Get product listing display modes
     */
    public static function getProductListModes()
    {
        return [
            self::LIST_MODE_HOVER_THUMBNAIL => 'Hover Thumbnail',
            self::LIST_MODE_HOVER_PREVIEW => 'Hover Preview',
            self::LIST_MODE_CLICK_POPUP => 'Click Popup',
            self::LIST_MODE_BADGE_ONLY => 'Badge Only',
            self::LIST_MODE_THUMBNAIL_CORNER => 'Corner Thumbnail',
            self::LIST_MODE_ICON_ONLY => 'Icon Only'
        ];
    }

    /**
     * Get layout configuration by ID
     */
    public static function getLayoutById($layout_id)
    {
        $combinations = self::getAllLayoutCombinations();
        return isset($combinations[$layout_id]) ? $combinations[$layout_id] : null;
    }

    /**
     * Get layouts by position
     */
    public static function getLayoutsByPosition($position)
    {
        $combinations = self::getAllLayoutCombinations();
        $filtered = [];
        
        foreach ($combinations as $combination) {
            if ($combination['position'] === $position) {
                $filtered[] = $combination;
            }
        }
        
        return $filtered;
    }

    /**
     * Get template variables for layout
     */
    public static function getTemplateVars($layout_config, $videos, $hook_name = '')
    {
        $vars = [
            'videos' => $videos,
            'hook_name' => $hook_name,
            'layout_style' => $layout_config['layout'],
            'display_mode' => $layout_config['mode'],
            'show_title' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_TITLE', 1),
            'show_video_title' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_VIDEO_TITLE', 1),
            'show_video_description' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_VIDEO_DESCRIPTION', 1),
            'show_video_info' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_VIDEO_INFO', 1),
            'show_share_buttons' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_SHARE_BUTTONS', 0),
            'show_view_all_link' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_SHOW_VIEW_ALL_LINK', 1),
            'max_videos_display' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_MAX_VIDEOS_DISPLAY', 5),
            'autoplay_carousel' => MultiShopHelper::getConfiguration('STPRODUCTVIDEO_AUTOPLAY_CAROUSEL', 0),
            'margin_top' => isset($layout_config['margin_top']) ? $layout_config['margin_top'] : 20,
            'margin_bottom' => isset($layout_config['margin_bottom']) ? $layout_config['margin_bottom'] : 20
        ];

        // Add layout-specific variables
        if (isset($layout_config['columns'])) {
            $vars['columns'] = $layout_config['columns'];
        }
        
        if (isset($layout_config['size'])) {
            $vars['size'] = $layout_config['size'];
        }
        
        if (isset($layout_config['alignment'])) {
            $vars['alignment'] = $layout_config['alignment'];
        }

        return $vars;
    }

    /**
     * Generate CSS for layout
     */
    public static function generateLayoutCSS($layout_config)
    {
        $css = '';
        $position = $layout_config['position'];
        $layout = $layout_config['layout'];
        $size = isset($layout_config['size']) ? $layout_config['size'] : 'medium';
        
        // Size-based CSS
        $sizes = [
            'small' => ['width' => '80px', 'height' => '60px', 'font_size' => '12px'],
            'medium' => ['width' => '120px', 'height' => '90px', 'font_size' => '14px'],
            'large' => ['width' => '160px', 'height' => '120px', 'font_size' => '16px']
        ];
        
        $size_config = $sizes[$size];
        
        if ($position === self::POSITION_PRODUCT_LIST) {
            // Product listing specific CSS
            $css .= ".product-list-video .video-thumbnail-overlay,\n";
            $css .= ".product-list-video .video-preview-container,\n";
            $css .= ".product-list-video .video-popup-trigger {\n";
            $css .= "    max-width: {$size_config['width']};\n";
            $css .= "    max-height: {$size_config['height']};\n";
            $css .= "}\n\n";
            
            $css .= ".product-list-video .video-badge-indicator {\n";
            $css .= "    font-size: {$size_config['font_size']};\n";
            $css .= "}\n\n";
            
            if (isset($layout_config['alignment'])) {
                $alignment = $layout_config['alignment'];
                if ($alignment === 'overlay') {
                    $css .= ".product-list-video {\n";
                    $css .= "    position: absolute;\n";
                    $css .= "    top: 10px;\n";
                    $css .= "    right: 10px;\n";
                    $css .= "    z-index: 10;\n";
                    $css .= "}\n\n";
                } else {
                    $css .= ".product-list-video {\n";
                    $css .= "    text-align: {$alignment};\n";
                    $css .= "}\n\n";
                }
            }
        } else {
            // Product page specific CSS
            if ($layout === self::LAYOUT_GRID && isset($layout_config['columns'])) {
                $columns = (int)$layout_config['columns'];
                $width = floor(100 / $columns);
                
                $css .= ".videos-grid .video-item-wrapper {\n";
                $css .= "    flex: 0 0 {$width}%;\n";
                $css .= "    max-width: {$width}%;\n";
                $css .= "}\n\n";
            }
            
            $css .= ".product-videos-container .video-title {\n";
            $css .= "    font-size: {$size_config['font_size']};\n";
            $css .= "}\n\n";
        }
        
        return $css;
    }

    /**
     * Get recommended layouts for position
     */
    public static function getRecommendedLayouts($position)
    {
        $recommendations = [
            self::POSITION_PRODUCT_IMAGES => [
                ['layout' => self::LAYOUT_CAROUSEL, 'mode' => self::MODE_THUMBNAIL, 'columns' => '1', 'size' => 'large'],
                ['layout' => self::LAYOUT_GRID, 'mode' => self::MODE_THUMBNAIL, 'columns' => '2', 'size' => 'medium'],
                ['layout' => self::LAYOUT_TABS, 'mode' => self::MODE_DIRECT, 'columns' => '1', 'size' => 'large']
            ],
            self::POSITION_ADDITIONAL_INFO => [
                ['layout' => self::LAYOUT_ACCORDION, 'mode' => self::MODE_THUMBNAIL, 'columns' => '1', 'size' => 'medium'],
                ['layout' => self::LAYOUT_LIST, 'mode' => self::MODE_THUMBNAIL, 'columns' => '1', 'size' => 'medium'],
                ['layout' => self::LAYOUT_TABS, 'mode' => self::MODE_POPUP, 'columns' => '1', 'size' => 'medium']
            ],
            self::POSITION_FOOTER => [
                ['layout' => self::LAYOUT_GRID, 'mode' => self::MODE_THUMBNAIL, 'columns' => '3', 'size' => 'small'],
                ['layout' => self::LAYOUT_CAROUSEL, 'mode' => self::MODE_THUMBNAIL, 'columns' => '1', 'size' => 'medium'],
                ['layout' => self::LAYOUT_LIST, 'mode' => self::MODE_POPUP, 'columns' => '1', 'size' => 'small']
            ],
            self::POSITION_EXTRA_CONTENT => [
                ['layout' => self::LAYOUT_GRID, 'mode' => self::MODE_DIRECT, 'columns' => '2', 'size' => 'medium'],
                ['layout' => self::LAYOUT_CAROUSEL, 'mode' => self::MODE_DIRECT, 'columns' => '1', 'size' => 'large'],
                ['layout' => self::LAYOUT_TABS, 'mode' => self::MODE_DIRECT, 'columns' => '1', 'size' => 'medium']
            ],
            self::POSITION_PRODUCT_LIST => [
                ['layout' => self::LIST_MODE_HOVER_THUMBNAIL, 'size' => 'small', 'alignment' => 'overlay'],
                ['layout' => self::LIST_MODE_HOVER_PREVIEW, 'size' => 'medium', 'alignment' => 'center'],
                ['layout' => self::LIST_MODE_CLICK_POPUP, 'size' => 'medium', 'alignment' => 'center']
            ]
        ];
        
        return isset($recommendations[$position]) ? $recommendations[$position] : [];
    }

    /**
     * Validate layout configuration
     */
    public static function validateLayout($layout_config)
    {
        $errors = [];
        
        if (!isset($layout_config['position']) || !in_array($layout_config['position'], array_keys(self::getPositions()))) {
            $errors[] = 'Invalid position';
        }
        
        if (!isset($layout_config['layout'])) {
            $errors[] = 'Layout style is required';
        }
        
        if ($layout_config['position'] === self::POSITION_PRODUCT_LIST) {
            if (!in_array($layout_config['layout'], array_keys(self::getProductListModes()))) {
                $errors[] = 'Invalid product listing layout';
            }
        } else {
            if (!in_array($layout_config['layout'], array_keys(self::getLayoutStyles()))) {
                $errors[] = 'Invalid layout style';
            }
            
            if (!isset($layout_config['mode']) || !in_array($layout_config['mode'], array_keys(self::getDisplayModes()))) {
                $errors[] = 'Invalid display mode';
            }
        }
        
        return $errors;
    }

    /**
     * Get layout preview HTML
     */
    public static function getLayoutPreview($layout_config)
    {
        $position = $layout_config['position'];
        $layout = $layout_config['layout'];
        
        $preview_html = '<div class="layout-preview">';
        $preview_html .= '<div class="preview-title">' . $layout_config['description'] . '</div>';
        $preview_html .= '<div class="preview-content">';
        
        if ($position === self::POSITION_PRODUCT_LIST) {
            $preview_html .= self::generateListPreview($layout_config);
        } else {
            $preview_html .= self::generatePagePreview($layout_config);
        }
        
        $preview_html .= '</div>';
        $preview_html .= '</div>';
        
        return $preview_html;
    }

    /**
     * Generate product listing preview
     */
    private static function generateListPreview($layout_config)
    {
        $layout = $layout_config['layout'];
        $size = $layout_config['size'];
        
        $html = '<div class="product-item-preview">';
        $html .= '<div class="product-image-placeholder"></div>';
        
        switch ($layout) {
            case self::LIST_MODE_HOVER_THUMBNAIL:
                $html .= '<div class="video-preview video-thumbnail-preview ' . $size . '"></div>';
                break;
            case self::LIST_MODE_HOVER_PREVIEW:
                $html .= '<div class="video-preview video-hover-preview ' . $size . '"></div>';
                break;
            case self::LIST_MODE_CLICK_POPUP:
                $html .= '<div class="video-preview video-popup-preview ' . $size . '"></div>';
                break;
            case self::LIST_MODE_BADGE_ONLY:
                $html .= '<div class="video-badge-preview"></div>';
                break;
            case self::LIST_MODE_THUMBNAIL_CORNER:
                $html .= '<div class="video-corner-preview ' . $size . '"></div>';
                break;
            case self::LIST_MODE_ICON_ONLY:
                $html .= '<div class="video-icon-preview"></div>';
                break;
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Generate product page preview
     */
    private static function generatePagePreview($layout_config)
    {
        $layout = $layout_config['layout'];
        $columns = isset($layout_config['columns']) ? (int)$layout_config['columns'] : 1;
        
        $html = '<div class="video-layout-preview ' . $layout . '">';
        
        switch ($layout) {
            case self::LAYOUT_LIST:
                for ($i = 0; $i < 3; $i++) {
                    $html .= '<div class="video-item-preview list-item"></div>';
                }
                break;
                
            case self::LAYOUT_GRID:
                $html .= '<div class="grid-container columns-' . $columns . '">';
                for ($i = 0; $i < min(6, $columns * 2); $i++) {
                    $html .= '<div class="video-item-preview grid-item"></div>';
                }
                $html .= '</div>';
                break;
                
            case self::LAYOUT_CAROUSEL:
                $html .= '<div class="carousel-preview">';
                $html .= '<div class="carousel-item active"></div>';
                $html .= '<div class="carousel-controls"></div>';
                $html .= '</div>';
                break;
                
            case self::LAYOUT_TABS:
                $html .= '<div class="tabs-preview">';
                $html .= '<div class="tab-headers">';
                for ($i = 0; $i < 3; $i++) {
                    $html .= '<div class="tab-header' . ($i === 0 ? ' active' : '') . '"></div>';
                }
                $html .= '</div>';
                $html .= '<div class="tab-content"></div>';
                $html .= '</div>';
                break;
                
            case self::LAYOUT_ACCORDION:
                for ($i = 0; $i < 3; $i++) {
                    $html .= '<div class="accordion-item' . ($i === 0 ? ' active' : '') . '">';
                    $html .= '<div class="accordion-header"></div>';
                    if ($i === 0) {
                        $html .= '<div class="accordion-content"></div>';
                    }
                    $html .= '</div>';
                }
                break;
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
