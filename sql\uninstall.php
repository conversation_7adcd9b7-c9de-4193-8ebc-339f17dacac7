<?php
/**
 * Database uninstallation script for ProductVideoPro module
 */

$sql = array();

// Drop all module tables
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_video_category`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_video_layout`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_video_settings`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_video_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_video`';

// Execute all SQL statements
foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}

// Remove configuration values
$config_keys = [
    'STPRODUCTVIDEO_ENABLED',
    'STPRODUCTVIDEO_DEFAULT_WIDTH',
    'STPRODUCTVIDEO_DEFAULT_HEIGHT',
    'STPRODUCTVIDEO_YOUTUBE_API_KEY',
    'STPRODUCTVIDEO_VIMEO_ACCESS_TOKEN',
    'STPRODUCTVIDEO_MAX_FILE_SIZE',
    'STPRODUCTVIDEO_ALLOWED_EXTENSIONS',
    'STPRODUCTVIDEO_THUMBNAIL_QUALITY',
    'STPRODUCTVIDEO_CACHE_ENABLED',
    'STPRODUCTVIDEO_LAZY_LOADING',
    'STPRODUCTVIDEO_AUTOPLAY_ENABLED',
    'STPRODUCTVIDEO_CONTROLS_ENABLED',
    'STPRODUCTVIDEO_RESPONSIVE_ENABLED'
];

foreach ($config_keys as $key) {
    Configuration::deleteByName($key);
}
