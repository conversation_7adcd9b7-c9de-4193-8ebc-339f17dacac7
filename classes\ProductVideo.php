<?php
/**
 * ProductVideo class for managing product videos
 */

class ProductVideo extends ObjectModel
{
    public $id_product_video;
    public $id_product;
    public $id_shop;
    public $video_type;
    public $video_url;
    public $video_id;
    public $thumbnail_url;
    public $width;
    public $height;
    public $position;
    public $active;
    public $autoplay;
    public $controls;
    public $loop;
    public $muted;
    public $show_in_product_page;
    public $show_in_product_list;
    public $show_in_additional_info;
    public $show_in_footer;
    public $show_in_extra_content;
    public $date_add;
    public $date_upd;

    // Multilingual fields
    public $title;
    public $description;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_product_video',
        'primary' => 'id_product_video',
        'multilang' => true,
        'multilang_shop' => true,
        'fields' => [
            'id_product' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_shop' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'video_type' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'values' => ['youtube', 'vimeo', 'mp4']],
            'video_url' => ['type' => self::TYPE_STRING, 'validate' => 'isUrl', 'required' => true, 'size' => 500],
            'video_id' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 100],
            'thumbnail_url' => ['type' => self::TYPE_STRING, 'validate' => 'isUrl', 'size' => 500],
            'width' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 20],
            'height' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 20],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'autoplay' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'controls' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'loop' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'muted' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'show_in_product_page' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'show_in_product_list' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'show_in_additional_info' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'show_in_footer' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'show_in_extra_content' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            
            // Multilingual fields
            'title' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'description' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get all videos for a specific product
     */
    public static function getProductVideos($id_product, $id_lang = null, $limit = null, $active_only = true)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('pv.*, pvl.title, pvl.description');
        $sql->from('st_product_video', 'pv');
        $sql->leftJoin('st_product_video_lang', 'pvl', 'pv.id_product_video = pvl.id_product_video AND pvl.id_lang = ' . (int)$id_lang);
        $sql->where('pv.id_product = ' . (int)$id_product);
        
        if ($active_only) {
            $sql->where('pv.active = 1');
        }
        
        if (Shop::isFeatureActive()) {
            $sql->where('pv.id_shop IS NULL OR pv.id_shop = ' . (int)Context::getContext()->shop->id);
        }
        
        $sql->orderBy('pv.position ASC, pv.id_product_video ASC');
        
        if ($limit) {
            $sql->limit((int)$limit);
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Get videos for product listing display
     */
    public static function getProductListVideos($id_product, $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = new DbQuery();
        $sql->select('pv.*, pvl.title, pvl.description');
        $sql->from('st_product_video', 'pv');
        $sql->leftJoin('st_product_video_lang', 'pvl', 'pv.id_product_video = pvl.id_product_video AND pvl.id_lang = ' . (int)$id_lang);
        $sql->where('pv.id_product = ' . (int)$id_product);
        $sql->where('pv.active = 1');
        $sql->where('pv.show_in_product_list = 1');
        
        if (Shop::isFeatureActive()) {
            $sql->where('pv.id_shop IS NULL OR pv.id_shop = ' . (int)Context::getContext()->shop->id);
        }
        
        $sql->orderBy('pv.position ASC');
        $sql->limit(1); // Only first video for product listings

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Update product videos from admin form
     */
    public static function updateProductVideos($id_product, $videos_data)
    {
        // First, get existing videos
        $existing_videos = self::getProductVideos($id_product, null, null, false);
        $existing_ids = array_column($existing_videos, 'id_product_video');

        $updated_ids = [];

        foreach ($videos_data as $video_data) {
            if (isset($video_data['id_product_video']) && $video_data['id_product_video']) {
                // Update existing video
                $video = new ProductVideo((int)$video_data['id_product_video']);
                $updated_ids[] = (int)$video_data['id_product_video'];
            } else {
                // Create new video
                $video = new ProductVideo();
                $video->id_product = (int)$id_product;
            }

            // Set video properties
            $video->video_type = pSQL($video_data['video_type']);
            $video->video_url = pSQL($video_data['video_url']);
            $video->video_id = isset($video_data['video_id']) ? pSQL($video_data['video_id']) : null;
            $video->thumbnail_url = isset($video_data['thumbnail_url']) ? pSQL($video_data['thumbnail_url']) : null;
            $video->width = isset($video_data['width']) ? pSQL($video_data['width']) : '100%';
            $video->height = isset($video_data['height']) ? pSQL($video_data['height']) : 'auto';
            $video->position = isset($video_data['position']) ? (int)$video_data['position'] : 0;
            $video->active = isset($video_data['active']) ? (bool)$video_data['active'] : true;
            $video->autoplay = isset($video_data['autoplay']) ? (bool)$video_data['autoplay'] : false;
            $video->controls = isset($video_data['controls']) ? (bool)$video_data['controls'] : true;
            $video->loop = isset($video_data['loop']) ? (bool)$video_data['loop'] : false;
            $video->muted = isset($video_data['muted']) ? (bool)$video_data['muted'] : false;
            $video->show_in_product_page = isset($video_data['show_in_product_page']) ? (bool)$video_data['show_in_product_page'] : true;
            $video->show_in_product_list = isset($video_data['show_in_product_list']) ? (bool)$video_data['show_in_product_list'] : false;
            $video->show_in_additional_info = isset($video_data['show_in_additional_info']) ? (bool)$video_data['show_in_additional_info'] : false;
            $video->show_in_footer = isset($video_data['show_in_footer']) ? (bool)$video_data['show_in_footer'] : false;
            $video->show_in_extra_content = isset($video_data['show_in_extra_content']) ? (bool)$video_data['show_in_extra_content'] : false;

            if (Shop::isFeatureActive()) {
                $video->id_shop = Context::getContext()->shop->id;
            }

            // Set multilingual fields
            if (isset($video_data['title'])) {
                $video->title = $video_data['title'];
            }
            if (isset($video_data['description'])) {
                $video->description = $video_data['description'];
            }

            $video->save();

            if (!isset($video_data['id_product_video']) || !$video_data['id_product_video']) {
                $updated_ids[] = $video->id;
            }
        }

        // Delete videos that were not updated (removed from form)
        $videos_to_delete = array_diff($existing_ids, $updated_ids);
        foreach ($videos_to_delete as $id_video) {
            $video = new ProductVideo($id_video);
            $video->delete();
        }

        return true;
    }

    /**
     * Delete all videos for a product
     */
    public static function deleteProductVideos($id_product)
    {
        $videos = self::getProductVideos($id_product, null, null, false);
        
        foreach ($videos as $video_data) {
            $video = new ProductVideo($video_data['id_product_video']);
            $video->delete();
        }

        return true;
    }

    /**
     * Copy videos from one product to another
     */
    public static function copyProductVideos($id_product_source, $id_product_target)
    {
        $source_videos = self::getProductVideos($id_product_source, null, null, false);
        
        foreach ($source_videos as $video_data) {
            $new_video = new ProductVideo();
            $new_video->id_product = (int)$id_product_target;
            $new_video->video_type = $video_data['video_type'];
            $new_video->video_url = $video_data['video_url'];
            $new_video->video_id = $video_data['video_id'];
            $new_video->thumbnail_url = $video_data['thumbnail_url'];
            $new_video->width = $video_data['width'];
            $new_video->height = $video_data['height'];
            $new_video->position = $video_data['position'];
            $new_video->active = $video_data['active'];
            $new_video->autoplay = $video_data['autoplay'];
            $new_video->controls = $video_data['controls'];
            $new_video->loop = $video_data['loop'];
            $new_video->muted = $video_data['muted'];
            $new_video->show_in_product_page = $video_data['show_in_product_page'];
            $new_video->show_in_product_list = $video_data['show_in_product_list'];
            $new_video->show_in_additional_info = $video_data['show_in_additional_info'];
            $new_video->show_in_footer = $video_data['show_in_footer'];
            $new_video->show_in_extra_content = $video_data['show_in_extra_content'];
            
            if (Shop::isFeatureActive()) {
                $new_video->id_shop = Context::getContext()->shop->id;
            }

            // Copy multilingual data
            $new_video->title = $video_data['title'];
            $new_video->description = $video_data['description'];

            $new_video->save();
        }

        return true;
    }
}
