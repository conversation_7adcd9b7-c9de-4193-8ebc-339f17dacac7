<?php
/**
 * ProductVideoPro Module for PrestaShop
 * 
 * <AUTHOR> Team
 * @copyright 2024 ProductVideoPro
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/classes/ProductVideo.php';
require_once dirname(__FILE__) . '/classes/ProductVideoLang.php';
require_once dirname(__FILE__) . '/classes/VideoSourceHandler.php';

class StProductVideo extends Module
{
    protected $config_form = false;
    
    public function __construct()
    {
        $this->name = 'stproductvideo';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = 'ProductVideoPro Team';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => '9.0.99'
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Product Video Pro');
        $this->description = $this->l('Add YouTube, Vimeo, or MP4 videos to product pages and listings with advanced layout options and multi-language support.');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall Product Video Pro? All video data will be lost.');
    }

    /**
     * Install the module
     */
    public function install()
    {
        include(dirname(__FILE__) . '/sql/install.php');

        return parent::install() &&
            $this->registerHook('header') &&
            $this->registerHook('backOfficeHeader') &&
            $this->registerHook('displayAdminProductsExtra') &&
            $this->registerHook('actionProductUpdate') &&
            $this->registerHook('actionProductDelete') &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('displayFooterProduct') &&
            $this->registerHook('displayProductExtraContent') &&
            $this->registerHook('displayProductListReviews') &&
            $this->registerHook('displayProductListFunctionalButtons') &&
            $this->registerHook('displayLeftColumnProduct') &&
            $this->registerHook('displayRightColumnProduct') &&
            $this->registerHook('displayProductButtons') &&
            $this->registerHook('displayProductTabContent') &&
            $this->registerHook('displayProductTab') &&
            $this->installTabs();
    }

    /**
     * Uninstall the module
     */
    public function uninstall()
    {
        include(dirname(__FILE__) . '/sql/uninstall.php');

        return parent::uninstall() && $this->uninstallTabs();
    }

    /**
     * Install admin tabs
     */
    private function installTabs()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminProductVideoPro';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Product Videos';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('AdminCatalog');
        $tab->module = $this->name;
        
        return $tab->add();
    }

    /**
     * Uninstall admin tabs
     */
    private function uninstallTabs()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminProductVideoPro');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        if (((bool)Tools::isSubmit('submitStProductVideoModule')) == true) {
            $this->postProcess();
        }

        $this->context->smarty->assign('module_dir', $this->_path);

        $output = $this->context->smarty->fetch($this->local_path . 'views/templates/admin/configure.tpl');

        return $output . $this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitStProductVideoModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = [
            'fields_value' => $this->getConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm([$this->getConfigForm()]);
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm()
    {
        return [
            'form' => [
                'legend' => [
                    'title' => $this->l('Settings'),
                    'icon' => 'icon-cogs',
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable Product Videos'),
                        'name' => 'STPRODUCTVIDEO_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->l('Enable or disable product videos globally.'),
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Default Video Width'),
                        'name' => 'STPRODUCTVIDEO_DEFAULT_WIDTH',
                        'size' => 20,
                        'required' => true,
                        'desc' => $this->l('Default width for videos (pixels or %). Example: 100% or 640px'),
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Default Video Height'),
                        'name' => 'STPRODUCTVIDEO_DEFAULT_HEIGHT',
                        'size' => 20,
                        'required' => true,
                        'desc' => $this->l('Default height for videos (pixels or %). Example: auto or 360px'),
                    ],
                ],
                'submit' => [
                    'title' => $this->l('Save'),
                ],
            ],
        ];
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues()
    {
        return [
            'STPRODUCTVIDEO_ENABLED' => Configuration::get('STPRODUCTVIDEO_ENABLED', true),
            'STPRODUCTVIDEO_DEFAULT_WIDTH' => Configuration::get('STPRODUCTVIDEO_DEFAULT_WIDTH', '100%'),
            'STPRODUCTVIDEO_DEFAULT_HEIGHT' => Configuration::get('STPRODUCTVIDEO_DEFAULT_HEIGHT', 'auto'),
        ];
    }

    /**
     * Save form data.
     */
    protected function postProcess()
    {
        $form_values = $this->getConfigFormValues();

        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
    }

    /**
     * Add CSS and JS to front office header
     */
    public function hookHeader()
    {
        if (!Configuration::get('STPRODUCTVIDEO_ENABLED')) {
            return;
        }

        $this->context->controller->addCSS($this->_path . 'views/css/front.css');
        $this->context->controller->addJS($this->_path . 'views/js/front.js');
    }

    /**
     * Add CSS and JS to back office header
     */
    public function hookBackOfficeHeader()
    {
        if (Tools::getValue('controller') == 'AdminProducts' ||
            Tools::getValue('controller') == 'AdminProductVideoPro') {
            $this->context->controller->addCSS($this->_path . 'views/css/back.css');
            $this->context->controller->addJS($this->_path . 'views/js/back.js');
        }
    }

    /**
     * Display videos in product additional info section
     */
    public function hookDisplayProductAdditionalInfo($params)
    {
        if (!Configuration::get('STPRODUCTVIDEO_ENABLED')) {
            return;
        }

        $product = $params['product'];
        $videos = ProductVideo::getProductVideos($product['id_product'], $this->context->language->id);

        if (empty($videos)) {
            return;
        }

        $this->context->smarty->assign([
            'videos' => $videos,
            'video_position' => 'additional_info'
        ]);

        return $this->display(__FILE__, 'product_videos.tpl');
    }

    /**
     * Display videos in product footer
     */
    public function hookDisplayFooterProduct($params)
    {
        if (!Configuration::get('STPRODUCTVIDEO_ENABLED')) {
            return;
        }

        $product = $params['product'];
        $videos = ProductVideo::getProductVideos($product['id_product'], $this->context->language->id);

        if (empty($videos)) {
            return;
        }

        $this->context->smarty->assign([
            'videos' => $videos,
            'video_position' => 'footer'
        ]);

        return $this->display(__FILE__, 'product_videos.tpl');
    }

    /**
     * Display videos in product extra content
     */
    public function hookDisplayProductExtraContent($params)
    {
        if (!Configuration::get('STPRODUCTVIDEO_ENABLED')) {
            return;
        }

        $product = $params['product'];
        $videos = ProductVideo::getProductVideos($product['id_product'], $this->context->language->id);

        if (empty($videos)) {
            return;
        }

        $this->context->smarty->assign([
            'videos' => $videos,
            'video_position' => 'extra_content'
        ]);

        return $this->display(__FILE__, 'product_videos.tpl');
    }

    /**
     * Display videos in product listings
     */
    public function hookDisplayProductListReviews($params)
    {
        if (!Configuration::get('STPRODUCTVIDEO_ENABLED')) {
            return;
        }

        $product = $params['product'];
        $videos = ProductVideo::getProductVideos($product['id_product'], $this->context->language->id, 1);

        if (empty($videos)) {
            return;
        }

        $this->context->smarty->assign([
            'videos' => $videos,
            'video_position' => 'product_list',
            'product' => $product
        ]);

        return $this->display(__FILE__, 'product_list_videos.tpl');
    }

    /**
     * Display video management in admin product page
     */
    public function hookDisplayAdminProductsExtra($params)
    {
        $id_product = (int)Tools::getValue('id_product');

        if (!$id_product) {
            return;
        }

        $videos = ProductVideo::getProductVideos($id_product);
        $languages = Language::getLanguages(false);

        $this->context->smarty->assign([
            'videos' => $videos,
            'languages' => $languages,
            'id_product' => $id_product,
            'token' => Tools::getAdminTokenLite('AdminProducts')
        ]);

        return $this->display(__FILE__, 'admin_product_videos.tpl');
    }

    /**
     * Handle product update actions
     */
    public function hookActionProductUpdate($params)
    {
        $id_product = (int)$params['id_product'];

        if (Tools::isSubmit('submitAddproduct') || Tools::isSubmit('submitAddproductAndStay')) {
            $this->processProductVideoUpdate($id_product);
        }
    }

    /**
     * Handle product deletion
     */
    public function hookActionProductDelete($params)
    {
        $id_product = (int)$params['id_product'];
        ProductVideo::deleteProductVideos($id_product);
    }

    /**
     * Process product video updates from admin
     */
    private function processProductVideoUpdate($id_product)
    {
        $videos = Tools::getValue('product_videos', []);

        if (!empty($videos)) {
            ProductVideo::updateProductVideos($id_product, $videos);
        }
    }
}
