{*
* Product Video Pro - Individual Video Item Template
*}

<div class="product-video-item" data-video-id="{$video.id_product_video}" data-video-type="{$video.video_type}">
    {if $video.title && $show_video_title}
        <h4 class="video-title">{$video.title|escape:'htmlall':'UTF-8'}</h4>
    {/if}
    
    {if $video.description && $show_video_description}
        <p class="video-description">{$video.description|escape:'htmlall':'UTF-8'|nl2br}</p>
    {/if}
    
    <div class="video-player-wrapper" style="position: relative;">
        {if $video.display_mode == 'thumbnail'}
            {* Thumbnail mode - click to play *}
            <div class="video-thumbnail-container" onclick="playVideo('{$video.id_product_video}', this)">
                <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                     alt="{if $video.title}{$video.title|escape:'htmlall':'UTF-8'}{else}{l s='Video thumbnail' mod='stproductvideo'}{/if}"
                     class="video-thumbnail img-responsive">
                <div class="video-play-overlay">
                    <div class="play-button">
                        <svg width="68" height="48" viewBox="0 0 68 48">
                            <path d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path>
                            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                        </svg>
                    </div>
                </div>
                {if $video.duration}
                    <div class="video-duration">{$video.duration}</div>
                {/if}
            </div>
            <div class="video-embed-container" style="display: none;">
                {$video.embed_code nofilter}
            </div>
        {elseif $video.display_mode == 'popup'}
            {* Popup mode - click to open modal *}
            <div class="video-thumbnail-container" onclick="openVideoModal('{$video.id_product_video}', '{$hook_name}')">
                <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                     alt="{if $video.title}{$video.title|escape:'htmlall':'UTF-8'}{else}{l s='Video thumbnail' mod='stproductvideo'}{/if}"
                     class="video-thumbnail img-responsive">
                <div class="video-play-overlay">
                    <div class="play-button popup-play">
                        <i class="fa fa-play-circle" style="font-size: 48px; color: rgba(255,255,255,0.9);"></i>
                    </div>
                </div>
                {if $video.duration}
                    <div class="video-duration">{$video.duration}</div>
                {/if}
            </div>
            <div class="video-embed-data" style="display: none;">{$video.embed_code nofilter}</div>
        {else}
            {* Direct embed mode *}
            <div class="video-embed-container">
                {$video.embed_code nofilter}
            </div>
        {/if}
    </div>
    
    {if $show_video_info}
        <div class="video-info mt-2">
            <div class="video-meta">
                {if $video.video_type == 'youtube'}
                    <span class="video-source">
                        <i class="fab fa-youtube" style="color: #ff0000;"></i>
                        YouTube
                    </span>
                {elseif $video.video_type == 'vimeo'}
                    <span class="video-source">
                        <i class="fab fa-vimeo" style="color: #1ab7ea;"></i>
                        Vimeo
                    </span>
                {elseif $video.video_type == 'mp4'}
                    <span class="video-source">
                        <i class="fas fa-file-video" style="color: #28a745;"></i>
                        MP4
                    </span>
                {/if}
                
                {if $video.duration}
                    <span class="video-duration-text">
                        <i class="far fa-clock"></i>
                        {$video.duration}
                    </span>
                {/if}
                
                {if $show_view_count && $video.view_count}
                    <span class="video-views">
                        <i class="far fa-eye"></i>
                        {$video.view_count|number_format} {l s='views' mod='stproductvideo'}
                    </span>
                {/if}
            </div>
            
            {if $show_share_buttons}
                <div class="video-share mt-2">
                    <span class="share-label">{l s='Share:' mod='stproductvideo'}</span>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={$video.video_url|escape:'url'}" 
                       target="_blank" class="share-btn facebook" title="{l s='Share on Facebook' mod='stproductvideo'}">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://twitter.com/intent/tweet?url={$video.video_url|escape:'url'}&text={if $video.title}{$video.title|escape:'url'}{else}{l s='Check out this video' mod='stproductvideo'}{/if}" 
                       target="_blank" class="share-btn twitter" title="{l s='Share on Twitter' mod='stproductvideo'}">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={$video.video_url|escape:'url'}" 
                       target="_blank" class="share-btn linkedin" title="{l s='Share on LinkedIn' mod='stproductvideo'}">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="mailto:?subject={if $video.title}{$video.title|escape:'url'}{else}{l s='Video' mod='stproductvideo'}{/if}&body={$video.video_url|escape:'url'}" 
                       class="share-btn email" title="{l s='Share via Email' mod='stproductvideo'}">
                        <i class="far fa-envelope"></i>
                    </a>
                </div>
            {/if}
        </div>
    {/if}
</div>

<style>
.product-video-item {
    margin-bottom: 20px;
}

.video-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.video-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.video-player-wrapper {
    position: relative;
    width: 100%;
}

.video-thumbnail-container {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.video-thumbnail-container:hover {
    transform: scale(1.02);
}

.video-thumbnail {
    width: 100%;
    height: auto;
    display: block;
}

.video-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.video-thumbnail-container:hover .video-play-overlay {
    background: rgba(0, 0, 0, 0.5);
}

.play-button {
    transition: transform 0.3s ease;
}

.video-thumbnail-container:hover .play-button {
    transform: scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.video-embed-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 8px;
}

.video-embed-container iframe,
.video-embed-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

.video-info {
    padding: 10px 0;
}

.video-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    font-size: 13px;
    color: #666;
}

.video-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.video-share {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.share-label {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.share-btn:hover {
    transform: scale(1.1);
    text-decoration: none;
    color: white;
}

.share-btn.facebook {
    background-color: #1877f2;
}

.share-btn.twitter {
    background-color: #1da1f2;
}

.share-btn.linkedin {
    background-color: #0077b5;
}

.share-btn.email {
    background-color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-title {
        font-size: 16px;
    }
    
    .video-description {
        font-size: 13px;
    }
    
    .video-meta {
        gap: 10px;
        font-size: 12px;
    }
    
    .share-btn {
        width: 28px;
        height: 28px;
    }
    
    .play-button svg {
        width: 48px;
        height: 36px;
    }
    
    .play-button.popup-play i {
        font-size: 36px !important;
    }
}

@media (max-width: 480px) {
    .video-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .video-share {
        gap: 8px;
    }
    
    .share-btn {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }
}
</style>
