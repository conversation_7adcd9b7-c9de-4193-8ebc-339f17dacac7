{*
* Product Video Pro - Admin Configuration Template
*}

<div class="panel">
    <h3><i class="icon icon-film"></i> {l s='Product Video Pro' mod='stproductvideo'}</h3>
    <p>
        <strong>{l s='Welcome to Product Video Pro!' mod='stproductvideo'}</strong><br />
        {l s='This module allows you to add YouTube, Vimeo, or MP4 videos to your product pages and listings with advanced layout options.' mod='stproductvideo'}
    </p>
    <br />
    <p>
        {l s='Key Features:' mod='stproductvideo'}
    </p>
    <ul>
        <li>{l s='Support for YouTube, Vimeo, and MP4 videos' mod='stproductvideo'}</li>
        <li>{l s='Multiple display positions on product pages' mod='stproductvideo'}</li>
        <li>{l s='Product listing video previews' mod='stproductvideo'}</li>
        <li>{l s='Multi-language video titles and descriptions' mod='stproductvideo'}</li>
        <li>{l s='Responsive video layouts' mod='stproductvideo'}</li>
        <li>{l s='Advanced video controls (autoplay, loop, muted)' mod='stproductvideo'}</li>
        <li>{l s='Drag-and-drop video ordering' mod='stproductvideo'}</li>
        <li>{l s='Copy videos between products' mod='stproductvideo'}</li>
    </ul>
</div>

<div class="panel">
    <h3><i class="icon icon-cogs"></i> {l s='Quick Actions' mod='stproductvideo'}</h3>
    <div class="row">
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-body text-center">
                    <i class="icon icon-plus-circle" style="font-size: 48px; color: #5cb85c;"></i>
                    <h4>{l s='Add Videos' mod='stproductvideo'}</h4>
                    <p>{l s='Add videos to your products from the product edit page or the dedicated video management section.' mod='stproductvideo'}</p>
                    <a href="{$link->getAdminLink('AdminProductVideoPro')}" class="btn btn-success">
                        {l s='Manage Videos' mod='stproductvideo'}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-body text-center">
                    <i class="icon icon-list" style="font-size: 48px; color: #5bc0de;"></i>
                    <h4>{l s='View Products' mod='stproductvideo'}</h4>
                    <p>{l s='Edit your products to add videos directly from the product management interface.' mod='stproductvideo'}</p>
                    <a href="{$link->getAdminLink('AdminProducts')}" class="btn btn-info">
                        {l s='Product Catalog' mod='stproductvideo'}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-body text-center">
                    <i class="icon icon-book" style="font-size: 48px; color: #f0ad4e;"></i>
                    <h4>{l s='Documentation' mod='stproductvideo'}</h4>
                    <p>{l s='Learn how to use all the features of Product Video Pro with our comprehensive guide.' mod='stproductvideo'}</p>
                    <a href="#" class="btn btn-warning" onclick="window.open('https://docs.productvideopro.com', '_blank');">
                        {l s='View Documentation' mod='stproductvideo'}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="panel">
    <h3><i class="icon icon-info-circle"></i> {l s='Usage Instructions' mod='stproductvideo'}</h3>
    <div class="alert alert-info">
        <h4>{l s='How to add videos to products:' mod='stproductvideo'}</h4>
        <ol>
            <li>{l s='Go to Catalog > Products and edit a product' mod='stproductvideo'}</li>
            <li>{l s='Scroll down to find the "Product Videos" section' mod='stproductvideo'}</li>
            <li>{l s='Click "Add Video" and enter your YouTube, Vimeo, or MP4 URL' mod='stproductvideo'}</li>
            <li>{l s='Configure display options and video settings' mod='stproductvideo'}</li>
            <li>{l s='Save the product to apply changes' mod='stproductvideo'}</li>
        </ol>
    </div>
    
    <div class="alert alert-warning">
        <h4>{l s='Supported Video Formats:' mod='stproductvideo'}</h4>
        <ul>
            <li><strong>YouTube:</strong> {l s='Any YouTube video URL (youtube.com or youtu.be)' mod='stproductvideo'}</li>
            <li><strong>Vimeo:</strong> {l s='Any Vimeo video URL (vimeo.com)' mod='stproductvideo'}</li>
            <li><strong>MP4:</strong> {l s='Direct links to MP4, WebM, or OGG video files' mod='stproductvideo'}</li>
        </ul>
    </div>
</div>

<div class="panel">
    <h3><i class="icon icon-wrench"></i> {l s='Advanced Configuration' mod='stproductvideo'}</h3>
    <div class="row">
        <div class="col-md-6">
            <h4>{l s='API Configuration' mod='stproductvideo'}</h4>
            <p>{l s='For enhanced features like video duration and better thumbnails, you can configure API keys:' mod='stproductvideo'}</p>
            <ul>
                <li><strong>YouTube API Key:</strong> {l s='Get from Google Cloud Console' mod='stproductvideo'}</li>
                <li><strong>Vimeo Access Token:</strong> {l s='Get from Vimeo Developer Portal' mod='stproductvideo'}</li>
            </ul>
        </div>
        <div class="col-md-6">
            <h4>{l s='Display Positions' mod='stproductvideo'}</h4>
            <p>{l s='Videos can be displayed in multiple positions:' mod='stproductvideo'}</p>
            <ul>
                <li>{l s='Product Images Section' mod='stproductvideo'}</li>
                <li>{l s='Product Additional Info' mod='stproductvideo'}</li>
                <li>{l s='Product Footer' mod='stproductvideo'}</li>
                <li>{l s='Product Extra Content' mod='stproductvideo'}</li>
                <li>{l s='Product Listings' mod='stproductvideo'}</li>
            </ul>
        </div>
    </div>
</div>

<style>
.panel .panel-body {
    min-height: 200px;
}
.panel .panel-body i {
    margin-bottom: 15px;
}
.alert ul, .alert ol {
    margin-bottom: 0;
}
</style>
