/**
 * Product Video Pro - Front Office JavaScript
 */

$(document).ready(function() {
    initializeProductVideos();
    initializeProductListVideos();
    initializeVideoModals();
});

/**
 * Initialize product page videos
 */
function initializeProductVideos() {
    // Initialize video carousels
    $('.videos-carousel').each(function() {
        var $carousel = $(this);
        var autoplay = $carousel.data('autoplay') || false;
        
        $carousel.carousel({
            interval: autoplay ? 5000 : false,
            pause: 'hover',
            wrap: true
        });
    });
    
    // Initialize video tabs
    $('.videos-tabs .nav-link').on('click', function(e) {
        e.preventDefault();
        $(this).tab('show');
    });
    
    // Initialize video accordions
    $('.videos-accordion .collapse').on('show.bs.collapse', function() {
        // Pause other videos when opening accordion
        pauseAllVideos();
    });
    
    // Initialize lazy loading for videos
    initializeLazyLoading();
}

/**
 * Initialize product listing videos
 */
function initializeProductListVideos() {
    // Initialize hover effects for product list videos
    $('.product-list-video').each(function() {
        var $container = $(this);
        var displayMode = $container.find('[data-display-mode]').data('display-mode');
        
        if (displayMode === 'hover_preview') {
            initializeHoverPreview($container);
        }
    });
}

/**
 * Initialize video modals
 */
function initializeVideoModals() {
    // Product page video modal
    $('.modal').on('hidden.bs.modal', function() {
        // Stop all videos when modal is closed
        $(this).find('iframe').each(function() {
            var src = $(this).attr('src');
            $(this).attr('src', src);
        });
        
        $(this).find('video').each(function() {
            this.pause();
            this.currentTime = 0;
        });
    });
}

/**
 * Initialize lazy loading for videos
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        var videoObserver = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var $video = $(entry.target);
                    loadVideo($video);
                    videoObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px'
        });
        
        $('.video-lazy-load').each(function() {
            videoObserver.observe(this);
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        $('.video-lazy-load').each(function() {
            loadVideo($(this));
        });
    }
}

/**
 * Load video content
 */
function loadVideo($videoContainer) {
    var embedCode = $videoContainer.data('embed-code');
    if (embedCode) {
        $videoContainer.html(embedCode);
        $videoContainer.removeClass('video-lazy-load');
    }
}

/**
 * Play video in product page
 */
function playVideo(videoId, element) {
    var $element = $(element);
    var $videoItem = $element.closest('.product-video-item');
    var $thumbnailContainer = $videoItem.find('.video-thumbnail-container');
    var $embedContainer = $videoItem.find('.video-embed-container');
    
    // Hide thumbnail and show video
    $thumbnailContainer.fadeOut(300, function() {
        $embedContainer.fadeIn(300);
        
        // Auto-focus on video for accessibility
        var $iframe = $embedContainer.find('iframe');
        if ($iframe.length) {
            $iframe.focus();
        }
    });
    
    // Track video play event
    trackVideoEvent('play', videoId, 'product_page');
}

/**
 * Open video modal
 */
function openVideoModal(videoId, hookName) {
    var $videoItem = $('[data-video-id="' + videoId + '"]');
    var $videoData = $videoItem.find('.video-embed-data');
    var embedCode = $videoData.html();
    var title = $videoItem.find('.video-title').text() || 'Product Video';
    
    var modalId = '#video-modal-' + hookName;
    var $modal = $(modalId);
    
    if ($modal.length === 0) {
        // Create modal if it doesn't exist
        $modal = createVideoModal(modalId);
        $('body').append($modal);
    }
    
    // Set modal content
    $modal.find('.modal-title').text(title);
    $modal.find('#modal-video-content').html(embedCode);
    
    // Show modal
    $modal.modal('show');
    
    // Track video modal open event
    trackVideoEvent('modal_open', videoId, hookName);
}

/**
 * Create video modal
 */
function createVideoModal(modalId) {
    var modalHtml = '<div class="modal fade" id="' + modalId.replace('#', '') + '" tabindex="-1" role="dialog" aria-hidden="true">' +
        '<div class="modal-dialog modal-lg modal-dialog-centered" role="document">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title">Video Player</h5>' +
        '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
        '<span aria-hidden="true">&times;</span>' +
        '</button>' +
        '</div>' +
        '<div class="modal-body">' +
        '<div id="modal-video-content"></div>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';
    
    return $(modalHtml);
}

/**
 * Show all videos (for view all functionality)
 */
function showAllVideos(hookName) {
    var $container = $('#product-videos-' + hookName);
    var $hiddenVideos = $container.find('.video-item-wrapper.hidden');
    
    $hiddenVideos.removeClass('hidden').fadeIn(300);
    $container.find('.videos-view-all').hide();
    
    // Track view all event
    trackVideoEvent('view_all', null, hookName);
}

/**
 * Product listing video functions
 */

/**
 * Show video thumbnail on hover
 */
function showVideoThumbnail(element) {
    var $element = $(element);
    $element.addClass('hover-active');
    
    // Add hover effect animation
    $element.find('.video-thumbnail-preview').css('transform', 'scale(1.05)');
}

/**
 * Hide video thumbnail on hover out
 */
function hideVideoThumbnail(element) {
    var $element = $(element);
    $element.removeClass('hover-active');
    
    // Remove hover effect animation
    $element.find('.video-thumbnail-preview').css('transform', 'scale(1)');
}

/**
 * Initialize hover preview for product listings
 */
function initializeHoverPreview($container) {
    var hoverTimer;
    var previewDelay = 1000; // 1 second delay before showing preview
    
    $container.on('mouseenter', function() {
        var $this = $(this);
        
        hoverTimer = setTimeout(function() {
            startVideoPreview($this[0]);
        }, previewDelay);
    });
    
    $container.on('mouseleave', function() {
        clearTimeout(hoverTimer);
        stopVideoPreview(this);
    });
}

/**
 * Start video preview on hover
 */
function startVideoPreview(element) {
    var $element = $(element);
    var $thumbnail = $element.find('.preview-image');
    var $embed = $element.find('.preview-embed');
    var $playButton = $element.find('.preview-play-button');
    
    // Hide thumbnail and play button, show video
    $thumbnail.fadeOut(200);
    $playButton.fadeOut(200);
    $embed.fadeIn(200);
    
    // Add autoplay parameter to video if it's an iframe
    var $iframe = $embed.find('iframe');
    if ($iframe.length) {
        var src = $iframe.attr('src');
        if (src && src.indexOf('autoplay=1') === -1) {
            var separator = src.indexOf('?') !== -1 ? '&' : '?';
            $iframe.attr('src', src + separator + 'autoplay=1&mute=1');
        }
    }
}

/**
 * Stop video preview on hover out
 */
function stopVideoPreview(element) {
    var $element = $(element);
    var $thumbnail = $element.find('.preview-image');
    var $embed = $element.find('.preview-embed');
    var $playButton = $element.find('.preview-play-button');
    
    // Hide video, show thumbnail and play button
    $embed.fadeOut(200);
    $thumbnail.fadeIn(200);
    $playButton.fadeIn(200);
    
    // Reset iframe src to stop video
    var $iframe = $embed.find('iframe');
    if ($iframe.length) {
        var src = $iframe.attr('src');
        if (src) {
            // Remove autoplay parameter
            src = src.replace(/[?&]autoplay=1/g, '').replace(/[?&]mute=1/g, '');
            $iframe.attr('src', src);
        }
    }
    
    // Pause HTML5 video
    var $video = $embed.find('video');
    if ($video.length) {
        $video[0].pause();
        $video[0].currentTime = 0;
    }
}

/**
 * Play video from product listing
 */
function playListVideo(videoId, element) {
    var $element = $(element);
    var $videoData = $element.closest('.product-list-video').find('.video-data');
    var title = $videoData.find('.video-title').text();
    var embedCode = $videoData.find('.video-embed').html();
    var videoUrl = $videoData.find('.video-url').text();
    
    // Open in modal
    openProductListVideoModal(videoId, title, embedCode, videoUrl);
    
    // Track video play from listing
    trackVideoEvent('play_from_listing', videoId, 'product_listing');
}

/**
 * Open video popup from product listing
 */
function openVideoPopup(videoId, productId) {
    playListVideo(videoId, event.target);
}

/**
 * Open product list video modal
 */
function openProductListVideoModal(videoId, title, embedCode, videoUrl) {
    var $modal = $('#product-list-video-modal');
    
    // Set modal content
    $modal.find('#video-modal-title').text(title || 'Product Video');
    $modal.find('#modal-video-player').html(embedCode);
    
    // Update view product link if available
    var $viewProductBtn = $modal.find('#modal-view-product');
    if (videoUrl) {
        $viewProductBtn.show();
        // This would need to be set based on the product URL
        // $viewProductBtn.attr('href', productUrl);
    } else {
        $viewProductBtn.hide();
    }
    
    // Show modal
    $modal.modal('show');
}

/**
 * Pause all videos
 */
function pauseAllVideos() {
    // Pause all iframes (YouTube, Vimeo)
    $('iframe').each(function() {
        var src = $(this).attr('src');
        if (src && (src.includes('youtube.com') || src.includes('vimeo.com'))) {
            // For YouTube and Vimeo, we can use postMessage API
            try {
                this.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                this.contentWindow.postMessage('{"method":"pause"}', '*');
            } catch (e) {
                // Fallback: reload iframe to stop video
                $(this).attr('src', src);
            }
        }
    });
    
    // Pause all HTML5 videos
    $('video').each(function() {
        this.pause();
    });
}

/**
 * Track video events for analytics
 */
function trackVideoEvent(action, videoId, context) {
    // Google Analytics tracking
    if (typeof gtag !== 'undefined') {
        gtag('event', 'video_' + action, {
            'event_category': 'Product Videos',
            'event_label': context,
            'value': videoId
        });
    }
    
    // Custom tracking
    if (typeof stproductvideo_track_events !== 'undefined' && stproductvideo_track_events) {
        $.ajax({
            url: stproductvideo_ajax_url,
            type: 'POST',
            data: {
                action: 'trackVideoEvent',
                video_action: action,
                video_id: videoId,
                context: context,
                ajax: true
            },
            dataType: 'json'
        });
    }
}

/**
 * Responsive video handling
 */
function handleResponsiveVideos() {
    $('.video-embed-container').each(function() {
        var $container = $(this);
        var $iframe = $container.find('iframe');
        var $video = $container.find('video');
        
        if ($iframe.length) {
            // Make iframe responsive
            $iframe.css({
                'width': '100%',
                'height': 'auto'
            });
        }
        
        if ($video.length) {
            // Make video responsive
            $video.css({
                'width': '100%',
                'height': 'auto'
            });
        }
    });
}

// Initialize responsive videos on window resize
$(window).on('resize', function() {
    handleResponsiveVideos();
});

// Initialize responsive videos on page load
$(document).ready(function() {
    handleResponsiveVideos();
});
