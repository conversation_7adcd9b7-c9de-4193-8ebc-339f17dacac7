{*
* Product Video Pro - Product Videos Display Template
*}

{if $videos && count($videos) > 0}
    <div id="product-videos-{$hook_name}" class="product-videos-container {$hook_name}-videos">
        {if $show_title}
            <h3 class="product-videos-title">
                {if $hook_name == 'displayProductAdditionalInfo'}
                    {l s='Product Videos' mod='stproductvideo'}
                {elseif $hook_name == 'displayFooterProduct'}
                    {l s='Related Videos' mod='stproductvideo'}
                {elseif $hook_name == 'displayProductExtraContent'}
                    {l s='Video Content' mod='stproductvideo'}
                {else}
                    {l s='Videos' mod='stproductvideo'}
                {/if}
            </h3>
        {/if}

        <div class="videos-wrapper layout-{$layout_style}">
            {if $layout_style == 'grid'}
                <div class="row videos-grid">
                    {foreach from=$videos item=video name=videoLoop}
                        <div class="col-md-{if count($videos) == 1}12{elseif count($videos) == 2}6{else}4{/if} video-item-wrapper">
                            {include file="module:stproductvideo/views/templates/hook/video_item.tpl" video=$video}
                        </div>
                    {/foreach}
                </div>
            {elseif $layout_style == 'carousel'}
                <div class="videos-carousel" id="videos-carousel-{$hook_name}">
                    <div class="carousel-inner">
                        {foreach from=$videos item=video name=videoLoop}
                            <div class="carousel-item {if $smarty.foreach.videoLoop.first}active{/if}">
                                {include file="module:stproductvideo/views/templates/hook/video_item.tpl" video=$video}
                            </div>
                        {/foreach}
                    </div>
                    {if count($videos) > 1}
                        <div class="carousel-controls">
                            <button class="carousel-control-prev" type="button" data-target="#videos-carousel-{$hook_name}" data-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                <span class="sr-only">{l s='Previous' mod='stproductvideo'}</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-target="#videos-carousel-{$hook_name}" data-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                <span class="sr-only">{l s='Next' mod='stproductvideo'}</span>
                            </button>
                        </div>
                        <div class="carousel-indicators">
                            {foreach from=$videos item=video name=indicatorLoop}
                                <button type="button" data-target="#videos-carousel-{$hook_name}" 
                                        data-slide-to="{$smarty.foreach.indicatorLoop.index}" 
                                        {if $smarty.foreach.indicatorLoop.first}class="active"{/if}></button>
                            {/foreach}
                        </div>
                    {/if}
                </div>
            {elseif $layout_style == 'tabs'}
                <div class="videos-tabs">
                    <ul class="nav nav-tabs" role="tablist">
                        {foreach from=$videos item=video name=tabLoop}
                            <li class="nav-item" role="presentation">
                                <a class="nav-link {if $smarty.foreach.tabLoop.first}active{/if}" 
                                   id="video-tab-{$video.id_product_video}" 
                                   data-toggle="tab" 
                                   href="#video-content-{$video.id_product_video}" 
                                   role="tab" 
                                   aria-controls="video-content-{$video.id_product_video}" 
                                   aria-selected="{if $smarty.foreach.tabLoop.first}true{else}false{/if}">
                                    {if $video.title}
                                        {$video.title|escape:'htmlall':'UTF-8'}
                                    {else}
                                        {l s='Video' mod='stproductvideo'} {$smarty.foreach.tabLoop.iteration}
                                    {/if}
                                </a>
                            </li>
                        {/foreach}
                    </ul>
                    <div class="tab-content">
                        {foreach from=$videos item=video name=contentLoop}
                            <div class="tab-pane fade {if $smarty.foreach.contentLoop.first}show active{/if}" 
                                 id="video-content-{$video.id_product_video}" 
                                 role="tabpanel" 
                                 aria-labelledby="video-tab-{$video.id_product_video}">
                                {include file="module:stproductvideo/views/templates/hook/video_item.tpl" video=$video}
                            </div>
                        {/foreach}
                    </div>
                </div>
            {elseif $layout_style == 'accordion'}
                <div class="videos-accordion" id="videos-accordion-{$hook_name}">
                    {foreach from=$videos item=video name=accordionLoop}
                        <div class="card">
                            <div class="card-header" id="video-heading-{$video.id_product_video}">
                                <h5 class="mb-0">
                                    <button class="btn btn-link {if !$smarty.foreach.accordionLoop.first}collapsed{/if}" 
                                            type="button" 
                                            data-toggle="collapse" 
                                            data-target="#video-collapse-{$video.id_product_video}" 
                                            aria-expanded="{if $smarty.foreach.accordionLoop.first}true{else}false{/if}" 
                                            aria-controls="video-collapse-{$video.id_product_video}">
                                        {if $video.title}
                                            {$video.title|escape:'htmlall':'UTF-8'}
                                        {else}
                                            {l s='Video' mod='stproductvideo'} {$smarty.foreach.accordionLoop.iteration}
                                        {/if}
                                    </button>
                                </h5>
                            </div>
                            <div id="video-collapse-{$video.id_product_video}" 
                                 class="collapse {if $smarty.foreach.accordionLoop.first}show{/if}" 
                                 aria-labelledby="video-heading-{$video.id_product_video}" 
                                 data-parent="#videos-accordion-{$hook_name}">
                                <div class="card-body">
                                    {include file="module:stproductvideo/views/templates/hook/video_item.tpl" video=$video}
                                </div>
                            </div>
                        </div>
                    {/foreach}
                </div>
            {else}
                {* Default list layout *}
                <div class="videos-list">
                    {foreach from=$videos item=video name=listLoop}
                        <div class="video-item-wrapper {if !$smarty.foreach.listLoop.last}mb-4{/if}">
                            {include file="module:stproductvideo/views/templates/hook/video_item.tpl" video=$video}
                        </div>
                    {/foreach}
                </div>
            {/if}
        </div>

        {if $show_view_all_link && count($videos) > $max_videos_display}
            <div class="videos-view-all text-center mt-3">
                <a href="#" class="btn btn-outline-primary" onclick="showAllVideos('{$hook_name}'); return false;">
                    {l s='View All Videos' mod='stproductvideo'} ({count($videos)})
                </a>
            </div>
        {/if}
    </div>

    {* Video Modal for popup display *}
    <div class="modal fade" id="video-modal-{$hook_name}" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel">{l s='Video Player' mod='stproductvideo'}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="modal-video-content"></div>
                </div>
            </div>
        </div>
    </div>

    {* Initialize carousel if needed *}
    {if $layout_style == 'carousel'}
        <script>
        $(document).ready(function() {
            $('#videos-carousel-{$hook_name}').carousel({
                interval: {if $autoplay_carousel}5000{else}false{/if},
                pause: 'hover'
            });
        });
        </script>
    {/if}

    {* Custom CSS for this hook *}
    <style>
    .product-videos-container.{$hook_name}-videos {
        margin: {$margin_top|default:'20'}px 0 {$margin_bottom|default:'20'}px 0;
    }
    
    {if $hook_name == 'displayProductAdditionalInfo'}
        .product-videos-container.displayProductAdditionalInfo-videos {
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    {elseif $hook_name == 'displayFooterProduct'}
        .product-videos-container.displayFooterProduct-videos {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
    {elseif $hook_name == 'displayProductExtraContent'}
        .product-videos-container.displayProductExtraContent-videos {
            border: 1px solid #e9ecef;
            padding: 20px;
            border-radius: 5px;
        }
    {/if}
    
    .videos-grid .video-item-wrapper {
        margin-bottom: 20px;
    }
    
    .videos-carousel .carousel-item {
        text-align: center;
    }
    
    .videos-tabs .nav-tabs {
        border-bottom: 2px solid #dee2e6;
    }
    
    .videos-tabs .nav-link {
        border: none;
        border-bottom: 2px solid transparent;
        color: #6c757d;
    }
    
    .videos-tabs .nav-link.active {
        border-bottom-color: #007bff;
        color: #007bff;
        background-color: transparent;
    }
    
    .videos-accordion .card {
        border: 1px solid #dee2e6;
        margin-bottom: 10px;
    }
    
    .videos-accordion .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .videos-accordion .btn-link {
        color: #495057;
        text-decoration: none;
        width: 100%;
        text-align: left;
    }
    
    .videos-accordion .btn-link:hover {
        color: #007bff;
        text-decoration: none;
    }
    
    .videos-list .video-item-wrapper {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 20px;
    }
    
    .videos-list .video-item-wrapper:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
    
    @media (max-width: 768px) {
        .videos-grid .col-md-4,
        .videos-grid .col-md-6 {
            flex: 0 0 100%;
            max-width: 100%;
        }
        
        .product-videos-container {
            margin: 15px 0;
        }
        
        .videos-tabs .nav-tabs {
            flex-wrap: wrap;
        }
        
        .videos-tabs .nav-link {
            font-size: 14px;
            padding: 8px 12px;
        }
    }
    </style>
{/if}
