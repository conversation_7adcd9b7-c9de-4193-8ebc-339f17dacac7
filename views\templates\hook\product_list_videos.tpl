{*
* Product Video Pro - Product List Videos Template
*}

{if $video}
    <div class="product-list-video" data-product-id="{$product.id_product}" data-video-id="{$video.id_product_video}">
        {if $display_mode == 'hover_thumbnail'}
            {* Hover to show video thumbnail *}
            <div class="video-hover-container">
                <div class="video-thumbnail-overlay" 
                     onmouseenter="showVideoThumbnail(this)" 
                     onmouseleave="hideVideoThumbnail(this)"
                     onclick="playListVideo('{$video.id_product_video}', this)">
                    <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                         alt="{l s='Video preview' mod='stproductvideo'}" 
                         class="video-thumbnail-preview">
                    <div class="video-play-indicator">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    {if $video.duration}
                        <span class="video-duration-badge">{$video.duration}</span>
                    {/if}
                </div>
            </div>
            
        {elseif $display_mode == 'hover_preview'}
            {* Hover to show video preview *}
            <div class="video-preview-container" 
                 onmouseenter="startVideoPreview(this)" 
                 onmouseleave="stopVideoPreview(this)"
                 onclick="playListVideo('{$video.id_product_video}', this)">
                <div class="video-preview-thumbnail">
                    <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                         alt="{l s='Video preview' mod='stproductvideo'}" 
                         class="preview-image">
                    <div class="video-preview-overlay">
                        <div class="preview-embed" style="display: none;">
                            {$video.embed_code_preview nofilter}
                        </div>
                        <div class="preview-play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    {if $video.duration}
                        <span class="video-duration-badge">{$video.duration}</span>
                    {/if}
                </div>
            </div>
            
        {elseif $display_mode == 'click_popup'}
            {* Click to open popup *}
            <div class="video-popup-trigger" onclick="openVideoPopup('{$video.id_product_video}', '{$product.id_product}')">
                <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                     alt="{l s='Video preview' mod='stproductvideo'}" 
                     class="video-thumbnail-clickable">
                <div class="video-popup-overlay">
                    <div class="popup-play-button">
                        <i class="fas fa-expand"></i>
                        <span>{l s='Watch Video' mod='stproductvideo'}</span>
                    </div>
                </div>
                {if $video.duration}
                    <span class="video-duration-badge">{$video.duration}</span>
                {/if}
            </div>
            
        {elseif $display_mode == 'badge_only'}
            {* Video badge indicator *}
            <div class="video-badge-indicator" onclick="playListVideo('{$video.id_product_video}', this)">
                <i class="fas fa-video"></i>
                <span>{l s='Video' mod='stproductvideo'}</span>
            </div>
            
        {elseif $display_mode == 'thumbnail_corner'}
            {* Small thumbnail in corner *}
            <div class="video-corner-thumbnail" onclick="playListVideo('{$video.id_product_video}', this)">
                <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                     alt="{l s='Video preview' mod='stproductvideo'}" 
                     class="corner-thumbnail-image">
                <div class="corner-play-overlay">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            
        {else}
            {* Default: Simple video icon *}
            <div class="video-icon-indicator" onclick="playListVideo('{$video.id_product_video}', this)">
                <i class="fas fa-play-circle"></i>
            </div>
        {/if}
        
        {* Hidden video data for popup/modal *}
        <div class="video-data" style="display: none;">
            <div class="video-title">{$video.title|escape:'htmlall':'UTF-8'}</div>
            <div class="video-embed">{$video.embed_code nofilter}</div>
            <div class="video-url">{$video.video_url|escape:'htmlall':'UTF-8'}</div>
        </div>
    </div>
{/if}

{* Video popup modal for product listings *}
<div class="modal fade" id="product-list-video-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="video-modal-title">{l s='Product Video' mod='stproductvideo'}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="modal-video-player"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{l s='Close' mod='stproductvideo'}</button>
                <a href="#" id="modal-view-product" class="btn btn-primary">{l s='View Product' mod='stproductvideo'}</a>
            </div>
        </div>
    </div>
</div>

<style>
.product-list-video {
    position: relative;
    z-index: 10;
}

/* Hover Thumbnail Mode */
.video-hover-container {
    position: relative;
    display: inline-block;
}

.video-thumbnail-overlay {
    position: relative;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
    max-width: 120px;
    max-height: 80px;
}

.video-thumbnail-overlay:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.video-thumbnail-preview {
    width: 100%;
    height: auto;
    display: block;
}

.video-play-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.video-thumbnail-overlay:hover .video-play-indicator {
    opacity: 1;
}

/* Hover Preview Mode */
.video-preview-container {
    position: relative;
    cursor: pointer;
    max-width: 150px;
    max-height: 100px;
}

.video-preview-thumbnail {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: auto;
    display: block;
    transition: opacity 0.3s ease;
}

.video-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.preview-embed {
    width: 100%;
    height: 100%;
}

.preview-embed iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.preview-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    background: rgba(0,0,0,0.6);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.video-preview-container:hover .preview-play-button {
    background: rgba(0,0,0,0.8);
    transform: translate(-50%, -50%) scale(1.1);
}

/* Click Popup Mode */
.video-popup-trigger {
    position: relative;
    cursor: pointer;
    border-radius: 6px;
    overflow: hidden;
    max-width: 140px;
    max-height: 90px;
}

.video-thumbnail-clickable {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.video-popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-popup-trigger:hover .video-popup-overlay {
    opacity: 1;
}

.popup-play-button {
    color: white;
    text-align: center;
}

.popup-play-button i {
    font-size: 24px;
    margin-bottom: 5px;
    display: block;
}

.popup-play-button span {
    font-size: 12px;
    font-weight: 500;
}

/* Badge Only Mode */
.video-badge-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.video-badge-indicator:hover {
    background: #0056b3;
}

.video-badge-indicator i {
    font-size: 14px;
}

/* Thumbnail Corner Mode */
.video-corner-thumbnail {
    position: relative;
    width: 60px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.corner-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.corner-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-corner-thumbnail:hover .corner-play-overlay {
    opacity: 1;
}

/* Default Icon Mode */
.video-icon-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: rgba(0,123,255,0.1);
    border: 2px solid #007bff;
    border-radius: 50%;
    color: #007bff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-icon-indicator:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

.video-icon-indicator i {
    font-size: 16px;
}

/* Duration Badge */
.video-duration-badge {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .video-thumbnail-overlay,
    .video-preview-container,
    .video-popup-trigger {
        max-width: 100px;
        max-height: 70px;
    }
    
    .video-corner-thumbnail {
        width: 50px;
        height: 35px;
    }
    
    .video-icon-indicator {
        width: 32px;
        height: 32px;
    }
    
    .video-icon-indicator i {
        font-size: 14px;
    }
    
    .video-play-indicator,
    .preview-play-button i,
    .popup-play-button i {
        font-size: 18px;
    }
    
    .preview-play-button {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .video-badge-indicator {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .video-badge-indicator i {
        font-size: 12px;
    }
    
    .popup-play-button span {
        font-size: 11px;
    }
}
</style>
