<?php
/**
 * VideoSourceHandler class for handling different video sources
 */

class VideoSourceHandler
{
    const YOUTUBE_REGEX = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';
    const VIMEO_REGEX = '/(?:vimeo\.com\/)([0-9]+)/i';

    /**
     * Detect video type from URL
     */
    public static function detectVideoType($url)
    {
        if (preg_match(self::YOUTUBE_REGEX, $url)) {
            return 'youtube';
        } elseif (preg_match(self::VIMEO_REGEX, $url)) {
            return 'vimeo';
        } elseif (self::isVideoFile($url)) {
            return 'mp4';
        }
        
        return false;
    }

    /**
     * Extract video ID from URL
     */
    public static function extractVideoId($url, $type = null)
    {
        if (!$type) {
            $type = self::detectVideoType($url);
        }

        switch ($type) {
            case 'youtube':
                if (preg_match(self::YOUTUBE_REGEX, $url, $matches)) {
                    return $matches[1];
                }
                break;
                
            case 'vimeo':
                if (preg_match(self::VIMEO_REGEX, $url, $matches)) {
                    return $matches[1];
                }
                break;
                
            case 'mp4':
                return basename($url);
        }

        return null;
    }

    /**
     * Get thumbnail URL for video
     */
    public static function getThumbnailUrl($url, $type = null, $video_id = null)
    {
        if (!$type) {
            $type = self::detectVideoType($url);
        }

        if (!$video_id) {
            $video_id = self::extractVideoId($url, $type);
        }

        switch ($type) {
            case 'youtube':
                return 'https://img.youtube.com/vi/' . $video_id . '/maxresdefault.jpg';
                
            case 'vimeo':
                return self::getVimeoThumbnail($video_id);
                
            case 'mp4':
                // For MP4 files, we'll need to generate thumbnails or use a placeholder
                return _MODULE_DIR_ . 'stproductvideo/views/img/video-placeholder.jpg';
        }

        return null;
    }

    /**
     * Get Vimeo thumbnail via API
     */
    private static function getVimeoThumbnail($video_id)
    {
        $api_url = 'https://vimeo.com/api/v2/video/' . $video_id . '.json';
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'ProductVideoPro/1.0'
            ]
        ]);
        
        $response = @file_get_contents($api_url, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data[0]['thumbnail_large'])) {
                return $data[0]['thumbnail_large'];
            }
        }
        
        // Fallback to placeholder
        return _MODULE_DIR_ . 'stproductvideo/views/img/video-placeholder.jpg';
    }

    /**
     * Generate embed code for video
     */
    public static function generateEmbedCode($video_data, $options = [])
    {
        $width = isset($options['width']) ? $options['width'] : ($video_data['width'] ?: '100%');
        $height = isset($options['height']) ? $options['height'] : ($video_data['height'] ?: 'auto');
        $autoplay = isset($options['autoplay']) ? $options['autoplay'] : $video_data['autoplay'];
        $controls = isset($options['controls']) ? $options['controls'] : $video_data['controls'];
        $loop = isset($options['loop']) ? $options['loop'] : $video_data['loop'];
        $muted = isset($options['muted']) ? $options['muted'] : $video_data['muted'];

        switch ($video_data['video_type']) {
            case 'youtube':
                return self::generateYouTubeEmbed($video_data['video_id'], $width, $height, $autoplay, $controls, $loop, $muted);
                
            case 'vimeo':
                return self::generateVimeoEmbed($video_data['video_id'], $width, $height, $autoplay, $controls, $loop, $muted);
                
            case 'mp4':
                return self::generateMP4Embed($video_data['video_url'], $width, $height, $autoplay, $controls, $loop, $muted);
        }

        return '';
    }

    /**
     * Generate YouTube embed code
     */
    private static function generateYouTubeEmbed($video_id, $width, $height, $autoplay, $controls, $loop, $muted)
    {
        $params = [];
        
        if ($autoplay) {
            $params[] = 'autoplay=1';
        }
        if (!$controls) {
            $params[] = 'controls=0';
        }
        if ($loop) {
            $params[] = 'loop=1&playlist=' . $video_id;
        }
        if ($muted) {
            $params[] = 'mute=1';
        }
        
        $params[] = 'rel=0';
        $params[] = 'modestbranding=1';
        
        $param_string = !empty($params) ? '?' . implode('&', $params) : '';
        
        $style = '';
        if ($height === 'auto') {
            $style = 'style="aspect-ratio: 16/9; width: ' . $width . ';"';
        } else {
            $style = 'style="width: ' . $width . '; height: ' . $height . ';"';
        }
        
        return '<iframe ' . $style . ' src="https://www.youtube.com/embed/' . $video_id . $param_string . '" 
                frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                allowfullscreen></iframe>';
    }

    /**
     * Generate Vimeo embed code
     */
    private static function generateVimeoEmbed($video_id, $width, $height, $autoplay, $controls, $loop, $muted)
    {
        $params = [];
        
        if ($autoplay) {
            $params[] = 'autoplay=1';
        }
        if (!$controls) {
            $params[] = 'controls=0';
        }
        if ($loop) {
            $params[] = 'loop=1';
        }
        if ($muted) {
            $params[] = 'muted=1';
        }
        
        $param_string = !empty($params) ? '?' . implode('&', $params) : '';
        
        $style = '';
        if ($height === 'auto') {
            $style = 'style="aspect-ratio: 16/9; width: ' . $width . ';"';
        } else {
            $style = 'style="width: ' . $width . '; height: ' . $height . ';"';
        }
        
        return '<iframe ' . $style . ' src="https://player.vimeo.com/video/' . $video_id . $param_string . '" 
                frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>';
    }

    /**
     * Generate MP4 embed code
     */
    private static function generateMP4Embed($video_url, $width, $height, $autoplay, $controls, $loop, $muted)
    {
        $attributes = [];
        
        if ($autoplay) {
            $attributes[] = 'autoplay';
        }
        if ($controls) {
            $attributes[] = 'controls';
        }
        if ($loop) {
            $attributes[] = 'loop';
        }
        if ($muted) {
            $attributes[] = 'muted';
        }
        
        $attributes[] = 'preload="metadata"';
        
        $style = '';
        if ($height === 'auto') {
            $style = 'style="width: ' . $width . '; height: auto;"';
        } else {
            $style = 'style="width: ' . $width . '; height: ' . $height . ';"';
        }
        
        return '<video ' . $style . ' ' . implode(' ', $attributes) . '>
                    <source src="' . $video_url . '" type="video/mp4">
                    Your browser does not support the video tag.
                </video>';
    }

    /**
     * Check if URL is a video file
     */
    private static function isVideoFile($url)
    {
        $video_extensions = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv'];
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        return in_array($extension, $video_extensions);
    }

    /**
     * Validate video URL
     */
    public static function validateVideoUrl($url)
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        $type = self::detectVideoType($url);
        if (!$type) {
            return false;
        }

        $video_id = self::extractVideoId($url, $type);
        if (!$video_id) {
            return false;
        }

        return [
            'type' => $type,
            'video_id' => $video_id,
            'thumbnail_url' => self::getThumbnailUrl($url, $type, $video_id)
        ];
    }

    /**
     * Get video duration (for supported platforms)
     */
    public static function getVideoDuration($video_id, $type)
    {
        switch ($type) {
            case 'youtube':
                return self::getYouTubeDuration($video_id);
                
            case 'vimeo':
                return self::getVimeoDuration($video_id);
                
            default:
                return null;
        }
    }

    /**
     * Get YouTube video duration
     */
    private static function getYouTubeDuration($video_id)
    {
        $api_key = Configuration::get('STPRODUCTVIDEO_YOUTUBE_API_KEY');
        if (!$api_key) {
            return null;
        }

        $api_url = 'https://www.googleapis.com/youtube/v3/videos?id=' . $video_id . '&part=contentDetails&key=' . $api_key;
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'ProductVideoPro/1.0'
            ]
        ]);
        
        $response = @file_get_contents($api_url, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['items'][0]['contentDetails']['duration'])) {
                return self::parseISO8601Duration($data['items'][0]['contentDetails']['duration']);
            }
        }
        
        return null;
    }

    /**
     * Get Vimeo video duration
     */
    private static function getVimeoDuration($video_id)
    {
        $api_url = 'https://vimeo.com/api/v2/video/' . $video_id . '.json';
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'ProductVideoPro/1.0'
            ]
        ]);
        
        $response = @file_get_contents($api_url, false, $context);
        
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data[0]['duration'])) {
                return (int)$data[0]['duration'];
            }
        }
        
        return null;
    }

    /**
     * Parse ISO 8601 duration format (YouTube)
     */
    private static function parseISO8601Duration($duration)
    {
        $interval = new DateInterval($duration);
        return ($interval->h * 3600) + ($interval->i * 60) + $interval->s;
    }
}
