/**
 * Product Video Pro - Admin JavaScript
 */

var videoCounter = 0;
var currentLanguage = 1;

$(document).ready(function() {
    initializeVideoManagement();
    initializeSortable();
    initializeLanguageTabs();
});

/**
 * Initialize video management functionality
 */
function initializeVideoManagement() {
    // Set initial video counter
    videoCounter = $('.video-item').length;
    
    // Initialize existing video items
    $('.video-item').each(function() {
        var $item = $(this);
        var $details = $item.find('.video-details');
        
        // Show first video details by default
        if ($item.is(':first-child')) {
            $details.show();
            $item.find('.icon-chevron-down').removeClass('icon-chevron-down').addClass('icon-chevron-up');
        }
    });
    
    // Hide no-videos message if videos exist
    if (videoCounter > 0) {
        $('#no-videos-message').hide();
    }
}

/**
 * Initialize sortable functionality for drag-and-drop
 */
function initializeSortable() {
    if (typeof $.fn.sortable !== 'undefined') {
        $('#videos-list').sortable({
            handle: '.video-handle',
            items: '.video-item',
            placeholder: 'video-placeholder',
            update: function(event, ui) {
                updateVideoPositions();
            }
        });
    }
}

/**
 * Initialize language tabs for multilingual fields
 */
function initializeLanguageTabs() {
    // Show only the first language by default
    $('.translation-field').hide();
    $('.translation-field[data-lang="' + currentLanguage + '"]').show();
    
    // Add language switcher if not exists
    if ($('.translatable-field').length > 0 && $('.lang-tabs').length === 0) {
        addLanguageTabs();
    }
}

/**
 * Add language tabs to translatable fields
 */
function addLanguageTabs() {
    var languages = [];
    $('.translation-field').each(function() {
        var langId = $(this).data('lang');
        var langImg = $(this).find('img').attr('src');
        var langName = $(this).find('img').attr('title');
        
        if (languages.findIndex(l => l.id === langId) === -1) {
            languages.push({
                id: langId,
                img: langImg,
                name: langName
            });
        }
    });
    
    if (languages.length > 1) {
        var tabsHtml = '<div class="lang-tabs" style="margin-bottom: 10px;">';
        languages.forEach(function(lang, index) {
            var activeClass = index === 0 ? 'active' : '';
            tabsHtml += '<button type="button" class="btn btn-default btn-sm lang-tab ' + activeClass + '" ' +
                       'data-lang="' + lang.id + '" onclick="switchLanguage(' + lang.id + ')">' +
                       '<img src="' + lang.img + '" alt="' + lang.name + '" style="width: 16px; height: 11px;"> ' +
                       lang.name + '</button> ';
        });
        tabsHtml += '</div>';
        
        $('.translatable-field').before(tabsHtml);
    }
}

/**
 * Switch language for multilingual fields
 */
function switchLanguage(langId) {
    currentLanguage = langId;
    
    $('.translation-field').hide();
    $('.translation-field[data-lang="' + langId + '"]').show();
    
    $('.lang-tab').removeClass('active');
    $('.lang-tab[data-lang="' + langId + '"]').addClass('active');
}

/**
 * Add new video item
 */
function addNewVideo() {
    videoCounter++;
    var newVideoId = 'new_' + videoCounter;
    
    var videoHtml = generateVideoItemHtml(newVideoId, {
        video_url: '',
        video_type: 'youtube',
        width: '100%',
        height: 'auto',
        position: videoCounter - 1,
        active: 1,
        controls: 1,
        show_in_product_page: 1
    });
    
    $('#videos-list').append(videoHtml);
    $('#no-videos-message').hide();
    
    // Initialize the new video item
    var $newItem = $('.video-item[data-video-id="' + newVideoId + '"]');
    $newItem.find('.video-details').show();
    $newItem.find('.icon-chevron-down').removeClass('icon-chevron-down').addClass('icon-chevron-up');
    
    // Scroll to new video
    $('html, body').animate({
        scrollTop: $newItem.offset().top - 100
    }, 500);
    
    updateVideoPositions();
}

/**
 * Generate HTML for video item
 */
function generateVideoItemHtml(videoId, videoData) {
    var isNew = videoId.startsWith('new_');
    var videoNumber = isNew ? videoCounter : videoId;
    
    var html = '<div class="video-item panel panel-default" data-video-id="' + videoId + '">';
    html += '<div class="panel-heading">';
    html += '<h4 class="panel-title">';
    html += '<span class="video-handle" style="cursor: move;"><i class="icon-move"></i></span>';
    html += 'Video #' + videoNumber;
    html += '<span class="video-type-badge badge badge-danger">' + videoData.video_type.toUpperCase() + '</span>';
    html += '<span class="pull-right">';
    html += '<a href="#" class="btn btn-default btn-xs" onclick="toggleVideoDetails(this); return false;">';
    html += '<i class="icon-chevron-down"></i></a>';
    html += '<a href="#" class="btn btn-danger btn-xs" onclick="removeVideo(this); return false;">';
    html += '<i class="icon-trash"></i></a>';
    html += '</span></h4></div>';
    
    html += '<div class="panel-body video-details" style="display: none;">';
    html += generateVideoFormFields(videoId, videoData);
    html += '</div></div>';
    
    return html;
}

/**
 * Generate form fields for video
 */
function generateVideoFormFields(videoId, videoData) {
    var html = '<div class="row"><div class="col-md-8">';
    
    // Video URL
    html += '<div class="form-group">';
    html += '<label class="control-label">Video URL <span class="required">*</span></label>';
    html += '<input type="text" name="product_videos[' + videoId + '][video_url]" ';
    html += 'value="' + (videoData.video_url || '') + '" ';
    html += 'class="form-control video-url-input" ';
    html += 'placeholder="Enter YouTube, Vimeo, or MP4 URL" ';
    html += 'onchange="validateVideoUrl(this)">';
    html += '</div>';
    
    // Video Type
    html += '<div class="form-group">';
    html += '<label class="control-label">Video Type</label>';
    html += '<select name="product_videos[' + videoId + '][video_type]" class="form-control">';
    html += '<option value="youtube"' + (videoData.video_type === 'youtube' ? ' selected' : '') + '>YouTube</option>';
    html += '<option value="vimeo"' + (videoData.video_type === 'vimeo' ? ' selected' : '') + '>Vimeo</option>';
    html += '<option value="mp4"' + (videoData.video_type === 'mp4' ? ' selected' : '') + '>MP4 File</option>';
    html += '</select></div>';
    
    // Add other form fields (title, description, width, height, position)
    html += generateMultilingualFields(videoId, 'title', 'Video Title', 'input');
    html += generateMultilingualFields(videoId, 'description', 'Video Description', 'textarea');
    
    html += '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<div class="form-group">';
    html += '<label class="control-label">Width</label>';
    html += '<input type="text" name="product_videos[' + videoId + '][width]" ';
    html += 'value="' + (videoData.width || '100%') + '" class="form-control" placeholder="100%">';
    html += '</div></div>';
    
    html += '<div class="col-md-6">';
    html += '<div class="form-group">';
    html += '<label class="control-label">Height</label>';
    html += '<input type="text" name="product_videos[' + videoId + '][height]" ';
    html += 'value="' + (videoData.height || 'auto') + '" class="form-control" placeholder="auto">';
    html += '</div></div></div>';
    
    html += '<div class="form-group">';
    html += '<label class="control-label">Position</label>';
    html += '<input type="number" name="product_videos[' + videoId + '][position]" ';
    html += 'value="' + (videoData.position || 0) + '" class="form-control" min="0" max="999">';
    html += '</div>';
    
    html += '</div><div class="col-md-4">';
    
    // Status switch
    html += '<div class="form-group">';
    html += '<label class="control-label">Status</label>';
    html += '<div class="switch prestashop-switch fixed-width-lg">';
    html += '<input type="radio" name="product_videos[' + videoId + '][active]" ';
    html += 'id="active_on_' + videoId + '" value="1"' + (videoData.active ? ' checked' : '') + '>';
    html += '<label for="active_on_' + videoId + '">Yes</label>';
    html += '<input type="radio" name="product_videos[' + videoId + '][active]" ';
    html += 'id="active_off_' + videoId + '" value="0"' + (!videoData.active ? ' checked' : '') + '>';
    html += '<label for="active_off_' + videoId + '">No</label>';
    html += '<a class="slide-button btn"></a>';
    html += '</div></div>';
    
    html += '</div></div>';
    
    // Add display options and video options panels
    html += generateOptionsPanel(videoId, videoData);
    
    // Hidden fields
    if (!videoId.startsWith('new_')) {
        html += '<input type="hidden" name="product_videos[' + videoId + '][id_product_video]" value="' + videoId + '">';
    }
    html += '<input type="hidden" name="product_videos[' + videoId + '][video_id]" value="">';
    html += '<input type="hidden" name="product_videos[' + videoId + '][thumbnail_url]" value="">';
    
    return html;
}

/**
 * Generate multilingual fields
 */
function generateMultilingualFields(videoId, fieldName, label, type) {
    var html = '<div class="form-group">';
    html += '<label class="control-label">' + label + '</label>';
    html += '<div class="translatable-field">';
    
    // This would need to be populated with actual language data
    // For now, just create a simple input for the default language
    html += '<div class="translation-field" data-lang="1">';
    html += '<div class="input-group">';
    html += '<span class="input-group-addon"><img src="../img/l/1.jpg" alt="Default"></span>';
    
    if (type === 'textarea') {
        html += '<textarea name="product_videos[' + videoId + '][' + fieldName + '][1]" ';
        html += 'class="form-control" rows="3" placeholder="' + label + '"></textarea>';
    } else {
        html += '<input type="text" name="product_videos[' + videoId + '][' + fieldName + '][1]" ';
        html += 'class="form-control" placeholder="' + label + '">';
    }
    
    html += '</div></div></div></div>';
    
    return html;
}

/**
 * Generate options panels
 */
function generateOptionsPanel(videoId, videoData) {
    var html = '<div class="panel panel-default">';
    html += '<div class="panel-heading"><h4 class="panel-title">Display Options</h4></div>';
    html += '<div class="panel-body"><div class="row">';
    
    var displayOptions = [
        {key: 'show_in_product_page', label: 'Show in product page'},
        {key: 'show_in_product_list', label: 'Show in product listings'},
        {key: 'show_in_additional_info', label: 'Show in additional info'},
        {key: 'show_in_footer', label: 'Show in product footer'},
        {key: 'show_in_extra_content', label: 'Show in extra content'}
    ];
    
    html += '<div class="col-md-6">';
    for (var i = 0; i < Math.ceil(displayOptions.length / 2); i++) {
        var option = displayOptions[i];
        html += '<div class="checkbox"><label>';
        html += '<input type="checkbox" name="product_videos[' + videoId + '][' + option.key + ']" value="1"';
        if (videoData[option.key]) html += ' checked';
        html += '> ' + option.label + '</label></div>';
    }
    html += '</div>';
    
    html += '<div class="col-md-6">';
    for (var i = Math.ceil(displayOptions.length / 2); i < displayOptions.length; i++) {
        var option = displayOptions[i];
        html += '<div class="checkbox"><label>';
        html += '<input type="checkbox" name="product_videos[' + videoId + '][' + option.key + ']" value="1"';
        if (videoData[option.key]) html += ' checked';
        html += '> ' + option.label + '</label></div>';
    }
    html += '</div>';
    
    html += '</div></div></div>';
    
    // Video options panel
    html += '<div class="panel panel-default">';
    html += '<div class="panel-heading"><h4 class="panel-title">Video Options</h4></div>';
    html += '<div class="panel-body"><div class="row">';
    
    var videoOptions = [
        {key: 'autoplay', label: 'Autoplay'},
        {key: 'controls', label: 'Show controls'},
        {key: 'loop', label: 'Loop video'},
        {key: 'muted', label: 'Muted by default'}
    ];
    
    html += '<div class="col-md-6">';
    for (var i = 0; i < Math.ceil(videoOptions.length / 2); i++) {
        var option = videoOptions[i];
        html += '<div class="checkbox"><label>';
        html += '<input type="checkbox" name="product_videos[' + videoId + '][' + option.key + ']" value="1"';
        if (videoData[option.key]) html += ' checked';
        html += '> ' + option.label + '</label></div>';
    }
    html += '</div>';
    
    html += '<div class="col-md-6">';
    for (var i = Math.ceil(videoOptions.length / 2); i < videoOptions.length; i++) {
        var option = videoOptions[i];
        html += '<div class="checkbox"><label>';
        html += '<input type="checkbox" name="product_videos[' + videoId + '][' + option.key + ']" value="1"';
        if (videoData[option.key]) html += ' checked';
        html += '> ' + option.label + '</label></div>';
    }
    html += '</div>';
    
    html += '</div></div></div>';
    
    return html;
}

/**
 * Toggle video details visibility
 */
function toggleVideoDetails(button) {
    var $button = $(button);
    var $details = $button.closest('.video-item').find('.video-details');
    var $icon = $button.find('i');
    
    if ($details.is(':visible')) {
        $details.slideUp();
        $icon.removeClass('icon-chevron-up').addClass('icon-chevron-down');
    } else {
        $details.slideDown();
        $icon.removeClass('icon-chevron-down').addClass('icon-chevron-up');
    }
}

/**
 * Remove video item
 */
function removeVideo(button) {
    if (confirm('Are you sure you want to remove this video?')) {
        var $videoItem = $(button).closest('.video-item');
        $videoItem.fadeOut(300, function() {
            $(this).remove();
            updateVideoPositions();
            
            if ($('.video-item').length === 0) {
                $('#no-videos-message').show();
            }
        });
    }
}

/**
 * Update video positions after reordering
 */
function updateVideoPositions() {
    $('.video-item').each(function(index) {
        $(this).find('input[name*="[position]"]').val(index);
    });
}

/**
 * Validate video URL and auto-detect type
 */
function validateVideoUrl(input) {
    var url = $(input).val();
    var $videoItem = $(input).closest('.video-item');
    var $typeSelect = $videoItem.find('select[name*="[video_type]"]');
    var $typeBadge = $videoItem.find('.video-type-badge');
    
    if (!url) return;
    
    // Auto-detect video type
    var type = 'mp4'; // default
    if (url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i)) {
        type = 'youtube';
    } else if (url.match(/(?:vimeo\.com\/)([0-9]+)/i)) {
        type = 'vimeo';
    }
    
    $typeSelect.val(type);
    $typeBadge.text(type.toUpperCase());
    
    // Update badge color
    $typeBadge.removeClass('badge-danger badge-info badge-success');
    if (type === 'youtube') {
        $typeBadge.addClass('badge-danger');
    } else if (type === 'vimeo') {
        $typeBadge.addClass('badge-info');
    } else {
        $typeBadge.addClass('badge-success');
    }
    
    // TODO: Fetch thumbnail and video ID via AJAX
    fetchVideoInfo(url, type, $videoItem);
}

/**
 * Fetch video information via AJAX
 */
function fetchVideoInfo(url, type, $videoItem) {
    if (!url || !type) return;

    $.ajax({
        url: stproductvideo_ajax_url,
        type: 'POST',
        data: {
            action: 'getVideoInfo',
            video_url: url,
            video_type: type,
            ajax: true
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update video ID and thumbnail
                $videoItem.find('input[name*="[video_id]"]').val(response.video_id);
                $videoItem.find('input[name*="[thumbnail_url]"]').val(response.thumbnail_url);

                // Update thumbnail display if exists
                var $thumbnailContainer = $videoItem.find('.video-thumbnail');
                if ($thumbnailContainer.length === 0) {
                    $videoItem.find('.col-md-4').prepend(
                        '<div class="video-thumbnail"><img src="' + response.thumbnail_url +
                        '" alt="Video thumbnail" class="img-responsive img-thumbnail"></div>'
                    );
                } else {
                    $thumbnailContainer.find('img').attr('src', response.thumbnail_url);
                }

                // Update duration if available
                if (response.duration) {
                    $videoItem.find('input[name*="[duration]"]').val(response.duration);
                }
            } else {
                console.error('Failed to fetch video info:', response.error);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', error);
        }
    });
}

/**
 * Copy videos from another product
 */
function copyVideosFromProduct() {
    $('#copyVideosModal').modal('show');
    loadProductsList();
}

/**
 * Load products list for copying
 */
function loadProductsList() {
    var $select = $('#source-product-select');
    $select.html('<option value="">' + stproductvideo_loading_text + '</option>');

    $.ajax({
        url: stproductvideo_ajax_url,
        type: 'POST',
        data: {
            action: 'getProductsWithVideos',
            current_product_id: stproductvideo_current_product_id,
            ajax: true
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">' + stproductvideo_choose_product_text + '</option>';
                $.each(response.products, function(index, product) {
                    options += '<option value="' + product.id_product + '">' +
                              product.name + ' (' + product.video_count + ' videos)</option>';
                });
                $select.html(options);

                // Add change event to show video preview
                $select.off('change').on('change', function() {
                    var productId = $(this).val();
                    if (productId) {
                        loadSourceVideosPreview(productId);
                    } else {
                        $('#source-videos-preview').hide();
                    }
                });
            } else {
                $select.html('<option value="">' + response.error + '</option>');
            }
        },
        error: function() {
            $select.html('<option value="">' + stproductvideo_error_text + '</option>');
        }
    });
}

/**
 * Load source videos preview
 */
function loadSourceVideosPreview(productId) {
    $.ajax({
        url: stproductvideo_ajax_url,
        type: 'POST',
        data: {
            action: 'getProductVideos',
            product_id: productId,
            ajax: true
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.videos.length > 0) {
                var html = '';
                $.each(response.videos, function(index, video) {
                    html += '<div class="source-video-item" style="margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">';
                    html += '<div class="row">';
                    html += '<div class="col-md-3">';
                    if (video.thumbnail_url) {
                        html += '<img src="' + video.thumbnail_url + '" alt="Video thumbnail" style="width: 100%; max-width: 80px;">';
                    }
                    html += '</div>';
                    html += '<div class="col-md-9">';
                    html += '<strong>' + (video.title || 'Video ' + (index + 1)) + '</strong><br>';
                    html += '<small>' + video.video_type.toUpperCase() + '</small>';
                    if (video.description) {
                        html += '<br><small>' + video.description.substring(0, 100) + '...</small>';
                    }
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                });

                $('#source-videos-list').html(html);
                $('#source-videos-preview').show();
            } else {
                $('#source-videos-preview').hide();
            }
        }
    });
}

/**
 * Execute copy videos operation
 */
function executeCopyVideos() {
    var sourceProductId = $('#source-product-select').val();
    if (!sourceProductId) {
        alert(stproductvideo_select_product_text);
        return;
    }

    $.ajax({
        url: stproductvideo_ajax_url,
        type: 'POST',
        data: {
            action: 'copyVideos',
            source_product_id: sourceProductId,
            target_product_id: stproductvideo_current_product_id,
            ajax: true
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#copyVideosModal').modal('hide');

                // Add copied videos to the form
                $.each(response.videos, function(index, video) {
                    var newVideoId = 'copied_' + video.id_product_video + '_' + Date.now();
                    var videoHtml = generateVideoItemHtml(newVideoId, video);
                    $('#videos-list').append(videoHtml);
                });

                $('#no-videos-message').hide();
                updateVideoPositions();

                // Show success message
                if (typeof showSuccessMessage === 'function') {
                    showSuccessMessage(response.message);
                } else {
                    alert(response.message);
                }
            } else {
                alert(response.error);
            }
        },
        error: function() {
            alert(stproductvideo_error_text);
        }
    });
}

/**
 * Reset all videos
 */
function resetAllVideos() {
    if (confirm(stproductvideo_confirm_reset_text)) {
        $('.video-item').fadeOut(300, function() {
            $(this).remove();
        });
        $('#no-videos-message').show();
        videoCounter = 0;
    }
}
