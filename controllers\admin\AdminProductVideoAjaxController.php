<?php
/**
 * AJAX Controller for Product Video Pro
 */

require_once _PS_MODULE_DIR_ . 'stproductvideo/classes/ProductVideo.php';
require_once _PS_MODULE_DIR_ . 'stproductvideo/classes/VideoSourceHandler.php';

class AdminProductVideoAjaxController extends ModuleAdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->bootstrap = true;
    }

    /**
     * Process AJAX requests
     */
    public function postProcess()
    {
        if (!Tools::isSubmit('ajax')) {
            return;
        }

        $action = Tools::getValue('action');
        
        switch ($action) {
            case 'getVideoInfo':
                $this->ajaxGetVideoInfo();
                break;
                
            case 'getProductsWithVideos':
                $this->ajaxGetProductsWithVideos();
                break;
                
            case 'getProductVideos':
                $this->ajaxGetProductVideos();
                break;
                
            case 'copyVideos':
                $this->ajaxCopyVideos();
                break;
                
            case 'uploadVideo':
                $this->ajaxUploadVideo();
                break;
                
            case 'deleteVideo':
                $this->ajaxDeleteVideo();
                break;
                
            case 'updateVideoPosition':
                $this->ajaxUpdateVideoPosition();
                break;
                
            case 'trackVideoEvent':
                $this->ajaxTrackVideoEvent();
                break;
                
            default:
                $this->ajaxResponse(false, 'Invalid action');
        }
    }

    /**
     * Get video information from URL
     */
    private function ajaxGetVideoInfo()
    {
        $video_url = Tools::getValue('video_url');
        $video_type = Tools::getValue('video_type');
        
        if (!$video_url) {
            $this->ajaxResponse(false, 'Video URL is required');
            return;
        }
        
        try {
            $validation = VideoSourceHandler::validateVideoUrl($video_url);
            
            if (!$validation) {
                $this->ajaxResponse(false, 'Invalid video URL');
                return;
            }
            
            $response_data = [
                'video_id' => $validation['video_id'],
                'video_type' => $validation['type'],
                'thumbnail_url' => $validation['thumbnail_url']
            ];
            
            // Get additional info like duration
            $duration = VideoSourceHandler::getVideoDuration($validation['video_id'], $validation['type']);
            if ($duration) {
                $response_data['duration'] = $this->formatDuration($duration);
            }
            
            $this->ajaxResponse(true, 'Video info retrieved successfully', $response_data);
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error retrieving video info: ' . $e->getMessage());
        }
    }

    /**
     * Get products that have videos
     */
    private function ajaxGetProductsWithVideos()
    {
        $current_product_id = (int)Tools::getValue('current_product_id');
        
        try {
            $sql = new DbQuery();
            $sql->select('p.id_product, pl.name, COUNT(pv.id_product_video) as video_count');
            $sql->from('product', 'p');
            $sql->leftJoin('product_lang', 'pl', 'p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id);
            $sql->leftJoin('st_product_video', 'pv', 'p.id_product = pv.id_product AND pv.active = 1');
            $sql->where('pl.id_shop = ' . (int)$this->context->shop->id);
            $sql->where('p.active = 1');
            if ($current_product_id) {
                $sql->where('p.id_product != ' . $current_product_id);
            }
            $sql->groupBy('p.id_product');
            $sql->having('video_count > 0');
            $sql->orderBy('pl.name ASC');
            
            $products = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            
            $this->ajaxResponse(true, 'Products retrieved successfully', ['products' => $products]);
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error retrieving products: ' . $e->getMessage());
        }
    }

    /**
     * Get videos for a specific product
     */
    private function ajaxGetProductVideos()
    {
        $product_id = (int)Tools::getValue('product_id');
        
        if (!$product_id) {
            $this->ajaxResponse(false, 'Product ID is required');
            return;
        }
        
        try {
            $videos = ProductVideo::getProductVideos($product_id, $this->context->language->id);
            
            $this->ajaxResponse(true, 'Videos retrieved successfully', ['videos' => $videos]);
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error retrieving videos: ' . $e->getMessage());
        }
    }

    /**
     * Copy videos from one product to another
     */
    private function ajaxCopyVideos()
    {
        $source_product_id = (int)Tools::getValue('source_product_id');
        $target_product_id = (int)Tools::getValue('target_product_id');
        
        if (!$source_product_id || !$target_product_id) {
            $this->ajaxResponse(false, 'Source and target product IDs are required');
            return;
        }
        
        try {
            $copied_videos = ProductVideo::copyProductVideos($source_product_id, $target_product_id);
            
            if ($copied_videos) {
                $message = sprintf(
                    $this->l('%d videos copied successfully'),
                    count($copied_videos)
                );
                $this->ajaxResponse(true, $message, ['videos' => $copied_videos]);
            } else {
                $this->ajaxResponse(false, 'No videos to copy or copy failed');
            }
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error copying videos: ' . $e->getMessage());
        }
    }

    /**
     * Upload video file (for MP4 uploads)
     */
    private function ajaxUploadVideo()
    {
        if (!isset($_FILES['video_file'])) {
            $this->ajaxResponse(false, 'No video file uploaded');
            return;
        }
        
        $file = $_FILES['video_file'];
        
        // Validate file
        $allowed_types = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!in_array($file['type'], $allowed_types)) {
            $this->ajaxResponse(false, 'Invalid video file type. Only MP4, WebM, and OGG are allowed.');
            return;
        }
        
        $max_size = Configuration::get('STPRODUCTVIDEO_MAX_FILE_SIZE', 50) * 1024 * 1024; // Default 50MB
        if ($file['size'] > $max_size) {
            $this->ajaxResponse(false, 'Video file is too large. Maximum size: ' . ($max_size / 1024 / 1024) . 'MB');
            return;
        }
        
        try {
            // Create upload directory if it doesn't exist
            $upload_dir = _PS_MODULE_DIR_ . 'stproductvideo/uploads/videos/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = uniqid('video_') . '.' . $extension;
            $filepath = $upload_dir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                $video_url = _MODULE_DIR_ . 'stproductvideo/uploads/videos/' . $filename;
                
                $this->ajaxResponse(true, 'Video uploaded successfully', [
                    'video_url' => $video_url,
                    'filename' => $filename
                ]);
            } else {
                $this->ajaxResponse(false, 'Failed to upload video file');
            }
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error uploading video: ' . $e->getMessage());
        }
    }

    /**
     * Delete video
     */
    private function ajaxDeleteVideo()
    {
        $video_id = (int)Tools::getValue('video_id');
        
        if (!$video_id) {
            $this->ajaxResponse(false, 'Video ID is required');
            return;
        }
        
        try {
            $video = new ProductVideo($video_id);
            if (!Validate::isLoadedObject($video)) {
                $this->ajaxResponse(false, 'Video not found');
                return;
            }
            
            if ($video->delete()) {
                $this->ajaxResponse(true, 'Video deleted successfully');
            } else {
                $this->ajaxResponse(false, 'Failed to delete video');
            }
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error deleting video: ' . $e->getMessage());
        }
    }

    /**
     * Update video positions
     */
    private function ajaxUpdateVideoPosition()
    {
        $video_positions = Tools::getValue('video_positions');
        
        if (!$video_positions || !is_array($video_positions)) {
            $this->ajaxResponse(false, 'Video positions data is required');
            return;
        }
        
        try {
            $updated_count = 0;
            
            foreach ($video_positions as $video_id => $position) {
                $video = new ProductVideo((int)$video_id);
                if (Validate::isLoadedObject($video)) {
                    $video->position = (int)$position;
                    if ($video->update()) {
                        $updated_count++;
                    }
                }
            }
            
            $this->ajaxResponse(true, sprintf('%d video positions updated', $updated_count));
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error updating video positions: ' . $e->getMessage());
        }
    }

    /**
     * Track video events for analytics
     */
    private function ajaxTrackVideoEvent()
    {
        $video_action = Tools::getValue('video_action');
        $video_id = (int)Tools::getValue('video_id');
        $context = Tools::getValue('context');
        
        try {
            // Log to database if tracking is enabled
            if (Configuration::get('STPRODUCTVIDEO_ENABLE_TRACKING')) {
                $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'st_product_video_stats` 
                        (id_product_video, action, context, ip_address, user_agent, date_add) 
                        VALUES (' . $video_id . ', "' . pSQL($video_action) . '", "' . pSQL($context) . '", 
                                "' . pSQL($_SERVER['REMOTE_ADDR']) . '", "' . pSQL($_SERVER['HTTP_USER_AGENT']) . '", NOW())';
                
                Db::getInstance()->execute($sql);
            }
            
            $this->ajaxResponse(true, 'Event tracked successfully');
            
        } catch (Exception $e) {
            $this->ajaxResponse(false, 'Error tracking event: ' . $e->getMessage());
        }
    }

    /**
     * Send AJAX response
     */
    private function ajaxResponse($success, $message, $data = [])
    {
        $response = [
            'success' => $success,
            'message' => $message
        ];
        
        if (!empty($data)) {
            $response = array_merge($response, $data);
        }
        
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    /**
     * Format duration in seconds to human readable format
     */
    private function formatDuration($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;
        
        if ($hours > 0) {
            return sprintf('%d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            return sprintf('%d:%02d', $minutes, $seconds);
        }
    }
}
