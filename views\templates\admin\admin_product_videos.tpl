{*
* Product Video Pro - Admin Product Videos Template
*}

<div id="product-videos-container" class="panel">
    <h3>
        <i class="icon-film"></i> {l s='Product Videos' mod='stproductvideo'}
        <span class="panel-heading-action">
            <a id="desc-product-new-video" class="list-toolbar-btn" href="#" onclick="addNewVideo(); return false;">
                <span title="" data-toggle="tooltip" class="label-tooltip" data-original-title="{l s='Add new video' mod='stproductvideo'}" data-html="true">
                    <i class="process-icon-new"></i>
                </span>
            </a>
        </span>
    </h3>
    
    <div class="alert alert-info">
        <p>{l s='Add YouTube, Vimeo, or MP4 videos to enhance your product presentation. Videos can be displayed in multiple positions on the product page and in product listings.' mod='stproductvideo'}</p>
    </div>

    <div id="videos-list" class="form-wrapper">
        {if $videos && count($videos) > 0}
            {foreach from=$videos item=video name=videoLoop}
                <div class="video-item panel panel-default" data-video-id="{$video.id_product_video|escape:'htmlall':'UTF-8'}">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <span class="video-handle" style="cursor: move;">
                                <i class="icon-move"></i>
                            </span>
                            {l s='Video' mod='stproductvideo'} #{$smarty.foreach.videoLoop.iteration}
                            <span class="video-type-badge badge badge-{if $video.video_type == 'youtube'}danger{elseif $video.video_type == 'vimeo'}info{else}success{/if}">
                                {$video.video_type|upper}
                            </span>
                            <span class="pull-right">
                                <a href="#" class="btn btn-default btn-xs" onclick="toggleVideoDetails(this); return false;">
                                    <i class="icon-chevron-down"></i>
                                </a>
                                <a href="#" class="btn btn-danger btn-xs" onclick="removeVideo(this); return false;">
                                    <i class="icon-trash"></i>
                                </a>
                            </span>
                        </h4>
                    </div>
                    <div class="panel-body video-details" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label class="control-label">{l s='Video URL' mod='stproductvideo'} <span class="required">*</span></label>
                                    <input type="text" name="product_videos[{$video.id_product_video}][video_url]" 
                                           value="{$video.video_url|escape:'htmlall':'UTF-8'}" 
                                           class="form-control video-url-input" 
                                           placeholder="{l s='Enter YouTube, Vimeo, or MP4 URL' mod='stproductvideo'}"
                                           onchange="validateVideoUrl(this)">
                                </div>
                                
                                <div class="form-group">
                                    <label class="control-label">{l s='Video Type' mod='stproductvideo'}</label>
                                    <select name="product_videos[{$video.id_product_video}][video_type]" class="form-control">
                                        <option value="youtube" {if $video.video_type == 'youtube'}selected{/if}>YouTube</option>
                                        <option value="vimeo" {if $video.video_type == 'vimeo'}selected{/if}>Vimeo</option>
                                        <option value="mp4" {if $video.video_type == 'mp4'}selected{/if}>MP4 File</option>
                                    </select>
                                </div>

                                {* Multi-language titles *}
                                <div class="form-group">
                                    <label class="control-label">{l s='Video Title' mod='stproductvideo'}</label>
                                    <div class="translatable-field">
                                        {foreach from=$languages item=language}
                                            <div class="translation-field" data-lang="{$language.id_lang}">
                                                <div class="input-group">
                                                    <span class="input-group-addon">
                                                        <img src="../img/l/{$language.id_lang}.jpg" alt="{$language.name}" title="{$language.name}">
                                                    </span>
                                                    <input type="text" 
                                                           name="product_videos[{$video.id_product_video}][title][{$language.id_lang}]" 
                                                           value="{if isset($video.title[$language.id_lang])}{$video.title[$language.id_lang]|escape:'htmlall':'UTF-8'}{/if}"
                                                           class="form-control" 
                                                           placeholder="{l s='Video title in' mod='stproductvideo'} {$language.name}">
                                                </div>
                                            </div>
                                        {/foreach}
                                    </div>
                                </div>

                                {* Multi-language descriptions *}
                                <div class="form-group">
                                    <label class="control-label">{l s='Video Description' mod='stproductvideo'}</label>
                                    <div class="translatable-field">
                                        {foreach from=$languages item=language}
                                            <div class="translation-field" data-lang="{$language.id_lang}">
                                                <div class="input-group">
                                                    <span class="input-group-addon">
                                                        <img src="../img/l/{$language.id_lang}.jpg" alt="{$language.name}" title="{$language.name}">
                                                    </span>
                                                    <textarea name="product_videos[{$video.id_product_video}][description][{$language.id_lang}]" 
                                                              class="form-control" rows="3"
                                                              placeholder="{l s='Video description in' mod='stproductvideo'} {$language.name}">{if isset($video.description[$language.id_lang])}{$video.description[$language.id_lang]|escape:'htmlall':'UTF-8'}{/if}</textarea>
                                                </div>
                                            </div>
                                        {/foreach}
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">{l s='Width' mod='stproductvideo'}</label>
                                            <input type="text" name="product_videos[{$video.id_product_video}][width]" 
                                                   value="{$video.width|escape:'htmlall':'UTF-8'}" 
                                                   class="form-control" 
                                                   placeholder="100%">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">{l s='Height' mod='stproductvideo'}</label>
                                            <input type="text" name="product_videos[{$video.id_product_video}][height]" 
                                                   value="{$video.height|escape:'htmlall':'UTF-8'}" 
                                                   class="form-control" 
                                                   placeholder="auto">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label">{l s='Position' mod='stproductvideo'}</label>
                                    <input type="number" name="product_videos[{$video.id_product_video}][position]" 
                                           value="{$video.position|escape:'htmlall':'UTF-8'}" 
                                           class="form-control" min="0" max="999">
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                {if $video.thumbnail_url}
                                    <div class="video-thumbnail">
                                        <img src="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}" 
                                             alt="{l s='Video thumbnail' mod='stproductvideo'}" 
                                             class="img-responsive img-thumbnail">
                                    </div>
                                {/if}
                                
                                <div class="form-group">
                                    <label class="control-label">{l s='Status' mod='stproductvideo'}</label>
                                    <div class="switch prestashop-switch fixed-width-lg">
                                        <input type="radio" name="product_videos[{$video.id_product_video}][active]" 
                                               id="active_on_{$video.id_product_video}" value="1" 
                                               {if $video.active}checked="checked"{/if}>
                                        <label for="active_on_{$video.id_product_video}">{l s='Yes' mod='stproductvideo'}</label>
                                        <input type="radio" name="product_videos[{$video.id_product_video}][active]" 
                                               id="active_off_{$video.id_product_video}" value="0" 
                                               {if !$video.active}checked="checked"{/if}>
                                        <label for="active_off_{$video.id_product_video}">{l s='No' mod='stproductvideo'}</label>
                                        <a class="slide-button btn"></a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {* Display Options *}
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">{l s='Display Options' mod='stproductvideo'}</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        {assign var="display_options" value=[
                                            'show_in_product_page' => 'Show in product page',
                                            'show_in_product_list' => 'Show in product listings',
                                            'show_in_additional_info' => 'Show in additional info'
                                        ]}
                                        {foreach from=$display_options key=option_key item=option_label}
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" 
                                                           name="product_videos[{$video.id_product_video}][{$option_key}]" 
                                                           value="1" 
                                                           {if $video.$option_key}checked{/if}>
                                                    {l s=$option_label mod='stproductvideo'}
                                                </label>
                                            </div>
                                        {/foreach}
                                    </div>
                                    <div class="col-md-6">
                                        {assign var="display_options2" value=[
                                            'show_in_footer' => 'Show in product footer',
                                            'show_in_extra_content' => 'Show in extra content'
                                        ]}
                                        {foreach from=$display_options2 key=option_key item=option_label}
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" 
                                                           name="product_videos[{$video.id_product_video}][{$option_key}]" 
                                                           value="1" 
                                                           {if $video.$option_key}checked{/if}>
                                                    {l s=$option_label mod='stproductvideo'}
                                                </label>
                                            </div>
                                        {/foreach}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {* Video Options *}
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">{l s='Video Options' mod='stproductvideo'}</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        {assign var="video_options" value=[
                                            'autoplay' => 'Autoplay',
                                            'controls' => 'Show controls'
                                        ]}
                                        {foreach from=$video_options key=option_key item=option_label}
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" 
                                                           name="product_videos[{$video.id_product_video}][{$option_key}]" 
                                                           value="1" 
                                                           {if $video.$option_key}checked{/if}>
                                                    {l s=$option_label mod='stproductvideo'}
                                                </label>
                                            </div>
                                        {/foreach}
                                    </div>
                                    <div class="col-md-6">
                                        {assign var="video_options2" value=[
                                            'loop' => 'Loop video',
                                            'muted' => 'Muted by default'
                                        ]}
                                        {foreach from=$video_options2 key=option_key item=option_label}
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" 
                                                           name="product_videos[{$video.id_product_video}][{$option_key}]" 
                                                           value="1" 
                                                           {if $video.$option_key}checked{/if}>
                                                    {l s=$option_label mod='stproductvideo'}
                                                </label>
                                            </div>
                                        {/foreach}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="product_videos[{$video.id_product_video}][id_product_video]" value="{$video.id_product_video}">
                        <input type="hidden" name="product_videos[{$video.id_product_video}][video_id]" value="{$video.video_id|escape:'htmlall':'UTF-8'}">
                        <input type="hidden" name="product_videos[{$video.id_product_video}][thumbnail_url]" value="{$video.thumbnail_url|escape:'htmlall':'UTF-8'}">
                    </div>
                </div>
            {/foreach}
        {else}
            <div id="no-videos-message" class="alert alert-warning">
                <p>{l s='No videos added yet. Click "Add Video" to get started.' mod='stproductvideo'}</p>
            </div>
        {/if}
    </div>

    <div class="panel-footer">
        <button type="button" class="btn btn-default" onclick="addNewVideo()">
            <i class="icon-plus"></i> {l s='Add Video' mod='stproductvideo'}
        </button>
        <button type="button" class="btn btn-info" onclick="copyVideosFromProduct()">
            <i class="icon-copy"></i> {l s='Copy from Another Product' mod='stproductvideo'}
        </button>
        <button type="button" class="btn btn-warning" onclick="resetAllVideos()">
            <i class="icon-refresh"></i> {l s='Reset All Videos' mod='stproductvideo'}
        </button>
    </div>
</div>

{* Copy Videos Modal *}
<div class="modal fade" id="copyVideosModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">{l s='Copy Videos from Another Product' mod='stproductvideo'}</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="source-product-select">{l s='Select Source Product' mod='stproductvideo'}</label>
                    <select id="source-product-select" class="form-control">
                        <option value="">{l s='Choose a product...' mod='stproductvideo'}</option>
                    </select>
                </div>
                <div id="source-videos-preview" style="display: none;">
                    <h5>{l s='Videos to copy:' mod='stproductvideo'}</h5>
                    <div id="source-videos-list"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{l s='Cancel' mod='stproductvideo'}</button>
                <button type="button" class="btn btn-primary" onclick="executeCopyVideos()">{l s='Copy Videos' mod='stproductvideo'}</button>
            </div>
        </div>
    </div>
</div>
