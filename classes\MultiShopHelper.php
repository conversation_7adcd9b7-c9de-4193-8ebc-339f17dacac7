<?php
/**
 * Multi-shop helper for Product Video Pro
 */

class MultiShopHelper
{
    /**
     * Get configuration value with multi-shop support
     */
    public static function getConfiguration($key, $default = null, $id_shop_group = null, $id_shop = null)
    {
        if (Shop::isFeatureActive()) {
            if ($id_shop === null) {
                $id_shop = Context::getContext()->shop->id;
            }
            if ($id_shop_group === null) {
                $id_shop_group = Context::getContext()->shop->id_shop_group;
            }
            
            // Try to get shop-specific configuration
            $value = Configuration::get($key, null, $id_shop_group, $id_shop);
            
            // If not found, try shop group configuration
            if ($value === null || $value === false) {
                $value = Configuration::get($key, null, $id_shop_group, null);
            }
            
            // If still not found, get global configuration
            if ($value === null || $value === false) {
                $value = Configuration::get($key);
            }
            
            return $value !== false ? $value : $default;
        }
        
        return Configuration::get($key, $default);
    }

    /**
     * Update configuration with multi-shop support
     */
    public static function updateConfiguration($key, $value, $id_shop_group = null, $id_shop = null)
    {
        if (Shop::isFeatureActive()) {
            if ($id_shop === null) {
                $id_shop = Context::getContext()->shop->id;
            }
            if ($id_shop_group === null) {
                $id_shop_group = Context::getContext()->shop->id_shop_group;
            }
            
            return Configuration::updateValue($key, $value, false, $id_shop_group, $id_shop);
        }
        
        return Configuration::updateValue($key, $value);
    }

    /**
     * Delete configuration with multi-shop support
     */
    public static function deleteConfiguration($key, $id_shop_group = null, $id_shop = null)
    {
        if (Shop::isFeatureActive()) {
            if ($id_shop === null) {
                $id_shop = Context::getContext()->shop->id;
            }
            if ($id_shop_group === null) {
                $id_shop_group = Context::getContext()->shop->id_shop_group;
            }
            
            return Configuration::deleteByName($key, $id_shop_group, $id_shop);
        }
        
        return Configuration::deleteByName($key);
    }

    /**
     * Get all shops for configuration management
     */
    public static function getShops($active = true)
    {
        if (!Shop::isFeatureActive()) {
            return [Context::getContext()->shop];
        }
        
        return Shop::getShops($active);
    }

    /**
     * Get shop groups for configuration management
     */
    public static function getShopGroups($active = true)
    {
        if (!Shop::isFeatureActive()) {
            return [Context::getContext()->shop->getGroup()];
        }
        
        return ShopGroup::getShopGroups($active);
    }

    /**
     * Check if current context is all shops
     */
    public static function isAllShopsContext()
    {
        return Shop::isFeatureActive() && Shop::getContext() == Shop::CONTEXT_ALL;
    }

    /**
     * Check if current context is shop group
     */
    public static function isShopGroupContext()
    {
        return Shop::isFeatureActive() && Shop::getContext() == Shop::CONTEXT_GROUP;
    }

    /**
     * Check if current context is single shop
     */
    public static function isShopContext()
    {
        return !Shop::isFeatureActive() || Shop::getContext() == Shop::CONTEXT_SHOP;
    }

    /**
     * Get current shop context ID
     */
    public static function getCurrentShopId()
    {
        return Context::getContext()->shop->id;
    }

    /**
     * Get current shop group context ID
     */
    public static function getCurrentShopGroupId()
    {
        return Context::getContext()->shop->id_shop_group;
    }

    /**
     * Copy configuration from one shop to another
     */
    public static function copyConfiguration($source_shop_id, $target_shop_id, $keys = [])
    {
        if (empty($keys)) {
            // Get all module configuration keys
            $keys = self::getModuleConfigurationKeys();
        }
        
        $source_shop = new Shop($source_shop_id);
        $target_shop = new Shop($target_shop_id);
        
        if (!Validate::isLoadedObject($source_shop) || !Validate::isLoadedObject($target_shop)) {
            return false;
        }
        
        $copied_count = 0;
        
        foreach ($keys as $key) {
            $value = Configuration::get($key, null, $source_shop->id_shop_group, $source_shop_id);
            if ($value !== false) {
                if (Configuration::updateValue($key, $value, false, $target_shop->id_shop_group, $target_shop_id)) {
                    $copied_count++;
                }
            }
        }
        
        return $copied_count;
    }

    /**
     * Get all module configuration keys
     */
    public static function getModuleConfigurationKeys()
    {
        return [
            'STPRODUCTVIDEO_ENABLED',
            'STPRODUCTVIDEO_DEFAULT_WIDTH',
            'STPRODUCTVIDEO_DEFAULT_HEIGHT',
            'STPRODUCTVIDEO_DEFAULT_LAYOUT',
            'STPRODUCTVIDEO_SHOW_TITLE',
            'STPRODUCTVIDEO_SHOW_DESCRIPTION',
            'STPRODUCTVIDEO_SHOW_VIDEO_INFO',
            'STPRODUCTVIDEO_SHOW_SHARE_BUTTONS',
            'STPRODUCTVIDEO_AUTOPLAY_CAROUSEL',
            'STPRODUCTVIDEO_MAX_VIDEOS_DISPLAY',
            'STPRODUCTVIDEO_ENABLE_LAZY_LOADING',
            'STPRODUCTVIDEO_ENABLE_TRACKING',
            'STPRODUCTVIDEO_YOUTUBE_API_KEY',
            'STPRODUCTVIDEO_VIMEO_ACCESS_TOKEN',
            'STPRODUCTVIDEO_MAX_FILE_SIZE',
            'STPRODUCTVIDEO_ALLOWED_FILE_TYPES',
            'STPRODUCTVIDEO_PRODUCT_LIST_DISPLAY_MODE',
            'STPRODUCTVIDEO_PRODUCT_LIST_MAX_VIDEOS',
            'STPRODUCTVIDEO_ENABLE_POPUP_MODAL',
            'STPRODUCTVIDEO_ENABLE_HOVER_PREVIEW',
            'STPRODUCTVIDEO_HOVER_PREVIEW_DELAY',
            'STPRODUCTVIDEO_CUSTOM_CSS',
            'STPRODUCTVIDEO_CUSTOM_JS'
        ];
    }

    /**
     * Inherit configuration from parent shop group
     */
    public static function inheritFromShopGroup($shop_id, $keys = [])
    {
        $shop = new Shop($shop_id);
        if (!Validate::isLoadedObject($shop)) {
            return false;
        }
        
        if (empty($keys)) {
            $keys = self::getModuleConfigurationKeys();
        }
        
        $inherited_count = 0;
        
        foreach ($keys as $key) {
            // Get value from shop group
            $value = Configuration::get($key, null, $shop->id_shop_group, null);
            if ($value !== false) {
                // Set value for specific shop
                if (Configuration::updateValue($key, $value, false, $shop->id_shop_group, $shop_id)) {
                    $inherited_count++;
                }
            }
        }
        
        return $inherited_count;
    }

    /**
     * Reset shop configuration to defaults
     */
    public static function resetToDefaults($shop_id = null, $keys = [])
    {
        if ($shop_id === null) {
            $shop_id = self::getCurrentShopId();
        }
        
        if (empty($keys)) {
            $keys = self::getModuleConfigurationKeys();
        }
        
        $defaults = self::getDefaultConfiguration();
        $reset_count = 0;
        
        foreach ($keys as $key) {
            $default_value = isset($defaults[$key]) ? $defaults[$key] : '';
            if (self::updateConfiguration($key, $default_value, null, $shop_id)) {
                $reset_count++;
            }
        }
        
        return $reset_count;
    }

    /**
     * Get default configuration values
     */
    public static function getDefaultConfiguration()
    {
        return [
            'STPRODUCTVIDEO_ENABLED' => 1,
            'STPRODUCTVIDEO_DEFAULT_WIDTH' => '100%',
            'STPRODUCTVIDEO_DEFAULT_HEIGHT' => 'auto',
            'STPRODUCTVIDEO_DEFAULT_LAYOUT' => 'list',
            'STPRODUCTVIDEO_SHOW_TITLE' => 1,
            'STPRODUCTVIDEO_SHOW_DESCRIPTION' => 1,
            'STPRODUCTVIDEO_SHOW_VIDEO_INFO' => 1,
            'STPRODUCTVIDEO_SHOW_SHARE_BUTTONS' => 0,
            'STPRODUCTVIDEO_AUTOPLAY_CAROUSEL' => 0,
            'STPRODUCTVIDEO_MAX_VIDEOS_DISPLAY' => 5,
            'STPRODUCTVIDEO_ENABLE_LAZY_LOADING' => 1,
            'STPRODUCTVIDEO_ENABLE_TRACKING' => 0,
            'STPRODUCTVIDEO_YOUTUBE_API_KEY' => '',
            'STPRODUCTVIDEO_VIMEO_ACCESS_TOKEN' => '',
            'STPRODUCTVIDEO_MAX_FILE_SIZE' => 50,
            'STPRODUCTVIDEO_ALLOWED_FILE_TYPES' => 'mp4,webm,ogg',
            'STPRODUCTVIDEO_PRODUCT_LIST_DISPLAY_MODE' => 'hover_thumbnail',
            'STPRODUCTVIDEO_PRODUCT_LIST_MAX_VIDEOS' => 1,
            'STPRODUCTVIDEO_ENABLE_POPUP_MODAL' => 1,
            'STPRODUCTVIDEO_ENABLE_HOVER_PREVIEW' => 1,
            'STPRODUCTVIDEO_HOVER_PREVIEW_DELAY' => 1000,
            'STPRODUCTVIDEO_CUSTOM_CSS' => '',
            'STPRODUCTVIDEO_CUSTOM_JS' => ''
        ];
    }

    /**
     * Get configuration differences between shops
     */
    public static function getConfigurationDifferences($shop1_id, $shop2_id, $keys = [])
    {
        if (empty($keys)) {
            $keys = self::getModuleConfigurationKeys();
        }
        
        $differences = [];
        
        foreach ($keys as $key) {
            $value1 = Configuration::get($key, null, null, $shop1_id);
            $value2 = Configuration::get($key, null, null, $shop2_id);
            
            if ($value1 !== $value2) {
                $differences[$key] = [
                    'shop1_value' => $value1,
                    'shop2_value' => $value2
                ];
            }
        }
        
        return $differences;
    }

    /**
     * Validate multi-shop configuration
     */
    public static function validateConfiguration($shop_id = null)
    {
        if ($shop_id === null) {
            $shop_id = self::getCurrentShopId();
        }
        
        $errors = [];
        $keys = self::getModuleConfigurationKeys();
        
        foreach ($keys as $key) {
            $value = self::getConfiguration($key, null, null, $shop_id);
            
            // Validate specific configuration values
            switch ($key) {
                case 'STPRODUCTVIDEO_DEFAULT_WIDTH':
                    if (empty($value)) {
                        $errors[] = 'Default width cannot be empty';
                    }
                    break;
                    
                case 'STPRODUCTVIDEO_DEFAULT_HEIGHT':
                    if (empty($value)) {
                        $errors[] = 'Default height cannot be empty';
                    }
                    break;
                    
                case 'STPRODUCTVIDEO_MAX_FILE_SIZE':
                    if (!is_numeric($value) || $value <= 0) {
                        $errors[] = 'Max file size must be a positive number';
                    }
                    break;
                    
                case 'STPRODUCTVIDEO_MAX_VIDEOS_DISPLAY':
                    if (!is_numeric($value) || $value <= 0) {
                        $errors[] = 'Max videos display must be a positive number';
                    }
                    break;
                    
                case 'STPRODUCTVIDEO_HOVER_PREVIEW_DELAY':
                    if (!is_numeric($value) || $value < 0) {
                        $errors[] = 'Hover preview delay must be a non-negative number';
                    }
                    break;
            }
        }
        
        return $errors;
    }
}
