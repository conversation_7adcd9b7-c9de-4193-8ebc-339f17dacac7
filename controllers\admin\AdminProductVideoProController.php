<?php
/**
 * Admin controller for Product Video Pro management
 */

require_once _PS_MODULE_DIR_ . 'stproductvideo/classes/ProductVideo.php';
require_once _PS_MODULE_DIR_ . 'stproductvideo/classes/VideoSourceHandler.php';

class AdminProductVideoProController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'st_product_video';
        $this->className = 'ProductVideo';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            ]
        ];

        $this->bootstrap = true;
        $this->meta_title = $this->l('Product Videos');

        parent::__construct();

        $this->fields_list = [
            'id_product_video' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'product_name' => [
                'title' => $this->l('Product'),
                'filter_key' => 'pl!name'
            ],
            'title' => [
                'title' => $this->l('Video Title'),
                'lang' => true
            ],
            'video_type' => [
                'title' => $this->l('Type'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ],
            'thumbnail' => [
                'title' => $this->l('Thumbnail'),
                'align' => 'center',
                'orderby' => false,
                'search' => false,
                'callback' => 'displayThumbnail'
            ],
            'position' => [
                'title' => $this->l('Position'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'active' => [
                'title' => $this->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ],
            'date_add' => [
                'title' => $this->l('Date Added'),
                'type' => 'datetime',
                'align' => 'right'
            ]
        ];
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_video'] = [
                'href' => self::$currentIndex . '&addst_product_video&token=' . $this->token,
                'desc' => $this->l('Add new video'),
                'icon' => 'process-icon-new'
            ];
        }

        parent::initPageHeaderToolbar();
    }

    /**
     * Render list with custom query
     */
    public function renderList()
    {
        $this->addRowAction('edit');
        $this->addRowAction('delete');

        $this->_select = 'pl.name as product_name';
        $this->_join = 'LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (a.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')';
        $this->_where = 'AND pl.id_shop = ' . (int)$this->context->shop->id;

        return parent::renderList();
    }

    /**
     * Display thumbnail callback
     */
    public function displayThumbnail($value, $row)
    {
        if (!empty($row['thumbnail_url'])) {
            return '<img src="' . $row['thumbnail_url'] . '" alt="Video thumbnail" style="max-width: 60px; max-height: 40px;">';
        }
        return '<span class="label label-warning">' . $this->l('No thumbnail') . '</span>';
    }

    /**
     * Render form for adding/editing videos
     */
    public function renderForm()
    {
        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Video Information'),
                'icon' => 'icon-film'
            ],
            'input' => [
                [
                    'type' => 'select',
                    'label' => $this->l('Product'),
                    'name' => 'id_product',
                    'required' => true,
                    'options' => [
                        'query' => $this->getProductsList(),
                        'id' => 'id_product',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Video Type'),
                    'name' => 'video_type',
                    'required' => true,
                    'options' => [
                        'query' => [
                            ['id' => 'youtube', 'name' => 'YouTube'],
                            ['id' => 'vimeo', 'name' => 'Vimeo'],
                            ['id' => 'mp4', 'name' => 'MP4 File']
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Video URL'),
                    'name' => 'video_url',
                    'required' => true,
                    'size' => 100,
                    'desc' => $this->l('Enter YouTube URL, Vimeo URL, or direct MP4 file URL')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                    'size' => 50,
                    'desc' => $this->l('Video title (optional)')
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'lang' => true,
                    'rows' => 3,
                    'desc' => $this->l('Video description (optional)')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Width'),
                    'name' => 'width',
                    'size' => 20,
                    'desc' => $this->l('Video width (e.g., 100%, 640px)')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Height'),
                    'name' => 'height',
                    'size' => 20,
                    'desc' => $this->l('Video height (e.g., auto, 360px)')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Position'),
                    'name' => 'position',
                    'size' => 10,
                    'desc' => $this->l('Display order (0 = first)')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'is_bool' => true,
                    'values' => [
                        ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Enabled')],
                        ['id' => 'active_off', 'value' => 0, 'label' => $this->l('Disabled')]
                    ]
                ]
            ]
        ];

        // Add display options
        $this->fields_form['input'][] = [
            'type' => 'html',
            'name' => 'display_options_separator',
            'html_content' => '<h3>' . $this->l('Display Options') . '</h3><hr>'
        ];

        $display_options = [
            'show_in_product_page' => $this->l('Show in product page'),
            'show_in_product_list' => $this->l('Show in product listings'),
            'show_in_additional_info' => $this->l('Show in additional info section'),
            'show_in_footer' => $this->l('Show in product footer'),
            'show_in_extra_content' => $this->l('Show in extra content section')
        ];

        foreach ($display_options as $field => $label) {
            $this->fields_form['input'][] = [
                'type' => 'switch',
                'label' => $label,
                'name' => $field,
                'is_bool' => true,
                'values' => [
                    ['id' => $field . '_on', 'value' => 1, 'label' => $this->l('Yes')],
                    ['id' => $field . '_off', 'value' => 0, 'label' => $this->l('No')]
                ]
            ];
        }

        // Add video options
        $this->fields_form['input'][] = [
            'type' => 'html',
            'name' => 'video_options_separator',
            'html_content' => '<h3>' . $this->l('Video Options') . '</h3><hr>'
        ];

        $video_options = [
            'autoplay' => $this->l('Autoplay'),
            'controls' => $this->l('Show controls'),
            'loop' => $this->l('Loop video'),
            'muted' => $this->l('Muted by default')
        ];

        foreach ($video_options as $field => $label) {
            $this->fields_form['input'][] = [
                'type' => 'switch',
                'label' => $label,
                'name' => $field,
                'is_bool' => true,
                'values' => [
                    ['id' => $field . '_on', 'value' => 1, 'label' => $this->l('Yes')],
                    ['id' => $field . '_off', 'value' => 0, 'label' => $this->l('No')]
                ]
            ];
        }

        $this->fields_form['submit'] = [
            'title' => $this->l('Save')
        ];

        return parent::renderForm();
    }

    /**
     * Get products list for dropdown
     */
    private function getProductsList()
    {
        $sql = new DbQuery();
        $sql->select('p.id_product, pl.name');
        $sql->from('product', 'p');
        $sql->leftJoin('product_lang', 'pl', 'p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id);
        $sql->where('pl.id_shop = ' . (int)$this->context->shop->id);
        $sql->where('p.active = 1');
        $sql->orderBy('pl.name ASC');

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Process form submission
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitAddst_product_video')) {
            $video_url = Tools::getValue('video_url');
            
            // Validate video URL
            $validation = VideoSourceHandler::validateVideoUrl($video_url);
            if (!$validation) {
                $this->errors[] = $this->l('Invalid video URL. Please enter a valid YouTube, Vimeo, or MP4 URL.');
                return false;
            }

            // Auto-detect video type and ID if not set
            if (!Tools::getValue('video_type')) {
                $_POST['video_type'] = $validation['type'];
            }
            if (!Tools::getValue('video_id')) {
                $_POST['video_id'] = $validation['video_id'];
            }
            if (!Tools::getValue('thumbnail_url')) {
                $_POST['thumbnail_url'] = $validation['thumbnail_url'];
            }
        }

        return parent::postProcess();
    }

    /**
     * Set default values for form
     */
    public function getFieldsValue($obj)
    {
        $fields_value = parent::getFieldsValue($obj);
        
        if (!isset($fields_value['width']) || empty($fields_value['width'])) {
            $fields_value['width'] = Configuration::get('STPRODUCTVIDEO_DEFAULT_WIDTH', '100%');
        }
        if (!isset($fields_value['height']) || empty($fields_value['height'])) {
            $fields_value['height'] = Configuration::get('STPRODUCTVIDEO_DEFAULT_HEIGHT', 'auto');
        }
        if (!isset($fields_value['active'])) {
            $fields_value['active'] = 1;
        }
        if (!isset($fields_value['controls'])) {
            $fields_value['controls'] = 1;
        }
        if (!isset($fields_value['show_in_product_page'])) {
            $fields_value['show_in_product_page'] = 1;
        }

        return $fields_value;
    }
}
